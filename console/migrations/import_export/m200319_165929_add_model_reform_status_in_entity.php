<?php

namespace console\migrations\import_export;

use common\models\Entity;
use yii\db\Migration;

/**
 * Class m200319_165929_add_model_reform_status_in_entity
 */
class m200319_165929_add_model_reform_status_in_entity extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->insert(Entity::tableName(), [
            'name' => 'reformStatus',
            'title' => 'Reform Status',
            'table_name' => 'reform_status',
            'page_name' => 'reformStatus',
            'class_name' => 'frontend\models\ReformStatus',
        ]);
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        echo "m200319_165929_add_model_reform_status_in_entity cannot be reverted.\n";

        return false;
    }
}
