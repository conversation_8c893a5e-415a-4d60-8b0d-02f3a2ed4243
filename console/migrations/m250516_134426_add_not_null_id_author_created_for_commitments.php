<?php

use yii\db\Migration;

/**
 * Class m250516_134426_add_not_null_id_author_created_for_commitments
 */
class m250516_134426_add_not_null_id_author_created_for_commitments extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->alterColumn('commitment_node', 'id_author', $this->integer()->notNull());
        $this->alterColumn('commitment_node', 'created_at', $this->timestamp()->notNull());
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        return true;
    }
}
