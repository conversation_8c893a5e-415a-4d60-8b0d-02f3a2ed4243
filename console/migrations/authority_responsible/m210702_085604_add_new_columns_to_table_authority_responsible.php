<?php

namespace console\migrations\authority_responsible;

use common\models\AuthorityResponsible;
use yii\db\Migration;

/**
 * Class m210702_085604_add_new_columns_to_table_authority_responsible
 */
class m210702_085604_add_new_columns_to_table_authority_responsible extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->addColumn(AuthorityResponsible::tableName(), 'person_uk', $this->string(1000)->defaultValue(null));
        $this->addColumn(AuthorityResponsible::tableName(), 'person_en', $this->string(1000)->defaultValue(null));
        $this->addColumn(AuthorityResponsible::tableName(), 'person_image', $this->text()->defaultValue(null));
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropColumn(AuthorityResponsible::tableName(), 'person_uk');
        $this->dropColumn(AuthorityResponsible::tableName(), 'person_en');
        $this->dropColumn(AuthorityResponsible::tableName(), 'person_image');
    }

}
