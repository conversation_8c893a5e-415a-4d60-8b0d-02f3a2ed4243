{"version": 1, "defects": {"tests\\unit\\models\\ApprovalRegistrationNumberTest::testGetMaxRegistrationNumberByReformId": 4, "tests\\unit\\models\\ApprovalRegistrationNumberTest::testAssignRegistrationNumber": 3, "tests\\unit\\models\\ApprovalRegistrationNumberTest::testSequentialAssignment": 3, "tests\\unit\\models\\ApprovalRegistrationNumberTest::testUniqueConstraint": 3, "tests\\unit\\models\\ApprovalRegistrationNumberTest::testConcurrentOperations": 3, "tests\\unit\\models\\ApprovalRegistrationNumberTest::testAssignRegistrationNumberWithoutReformId": 4, "tests\\unit\\models\\ApprovalRegistrationNumberTest::testGetFormattedRegistrationNumber": 4, "tests\\unit\\models\\ApprovalRegistrationNumberTest::testGetRegistrationDateTime": 4, "tests\\unit\\models\\ApprovalRegistrationNumberTest::testMaxRetryAttempts": 4, "tests\\unit\\migrations\\ApprovalRegistrationMigrationTest::testAddColumnOperation": 4, "tests\\unit\\migrations\\ApprovalRegistrationMigrationTest::testCreateIndexOperation": 4, "tests\\unit\\models\\ApprovalRegistrationTest::testGetMaxRegistrationNumberByReformId": 4, "tests\\unit\\models\\ApprovalRegistrationTest::testGetMaxRegistrationNumberReturnsZeroWhenNoRegistrations": 3, "tests\\unit\\models\\ApprovalRegistrationTest::testAssignRegistrationNumberSkipsNonConfirmedApproval": 4, "tests\\unit\\models\\ApprovalRegistrationTest::testAssignRegistrationNumberSuccess": 4, "tests\\unit\\models\\ApprovalRegistrationTest::testAssignRegistrationNumberThrowsExceptionAfterMaxAttempts": 3, "tests\\unit\\models\\ApprovalRegistrationTest::testGetFormattedRegistrationNumber": 4, "tests\\unit\\models\\ApprovalRegistrationTest::testGetRegistrationDateTime": 3, "tests\\integration\\ApprovalRegistrationNumberDbTest::testCompleteApprovalWorkflow": 4, "tests\\integration\\ApprovalRegistrationNumberDbTest::testSequentialRegistrationNumbers": 4, "tests\\integration\\ApprovalRegistrationNumberDbTest::testRegistrationNumbersAcrossMultipleReforms": 3, "tests\\integration\\ApprovalRegistrationNumberDbTest::testRegistrationNumberUniqueness": 3, "tests\\integration\\ApprovalRegistrationNumberDbTest::testTransactionHandling": 3, "tests\\integration\\ApprovalRegistrationNumberDbTest::testRecoveryFromFailures": 3, "tests\\integration\\ApprovalSigningIntegrationTest::testSigningProcessWithRegistration": 4, "tests\\integration\\ApprovalSigningIntegrationTest::testSequentialSigning": 4, "tests\\integration\\ApprovalSigningIntegrationTest::testTransactionRollback": 4, "tests\\unit\\models\\ApprovalRegistrationTest::testAssignRegistrationNumberByNodeSuccess": 3, "tests\\unit\\services\\RegistrationNumberServiceTest::testSingletonInstance": 4, "tests\\unit\\services\\RegistrationNumberServiceTest::testGetNextAvailableNumber": 4, "tests\\unit\\services\\RegistrationNumberServiceTest::testReserveNumber": 4, "tests\\unit\\services\\RegistrationNumberServiceTest::testReserveAlreadyTakenNumber": 4, "tests\\unit\\services\\RegistrationNumberServiceTest::testConfirmReservedNumber": 4, "tests\\unit\\services\\RegistrationNumberServiceTest::testReleaseReservedNumber": 4, "tests\\unit\\services\\RegistrationNumberServiceTest::testGetMaxNumber": 4, "tests\\unit\\services\\RegistrationNumberServiceTest::testCleanupExpiredReservations": 4, "ApprovalRegistrationNumberTest::testGetMaxRegistrationNumberByReformId": 4, "frontend\\tests\\unit\\models\\ResetPasswordFormTest::testResetWrongToken": 4, "frontend\\tests\\unit\\models\\ResetPasswordFormTest::testResetCorrectToken": 4, "ApprovalRegistrationNumberTest::testSignOneFolder": 4, "ApprovalRegistrationNumberTest::testSignManyFolders": 4}, "times": {"tests\\unit\\models\\ApprovalRegistrationNumberTest::testGetMaxRegistrationNumberByReformId": 38.386, "tests\\unit\\models\\ApprovalRegistrationNumberTest::testAssignRegistrationNumber": 0.126, "tests\\unit\\models\\ApprovalRegistrationNumberTest::testSequentialAssignment": 0.007, "tests\\unit\\models\\ApprovalRegistrationNumberTest::testUniqueConstraint": 0.098, "tests\\unit\\models\\ApprovalRegistrationNumberTest::testConcurrentOperations": 0.007, "tests\\unit\\models\\ApprovalRegistrationNumberTest::testAssignRegistrationNumberWithoutReformId": 0.008, "tests\\unit\\models\\ApprovalRegistrationNumberTest::testGetFormattedRegistrationNumber": 0.001, "tests\\unit\\models\\ApprovalRegistrationNumberTest::testGetRegistrationDateTime": 0.001, "tests\\unit\\models\\ApprovalRegistrationNumberTest::testMaxRetryAttempts": 0.324, "tests\\unit\\migrations\\ApprovalRegistrationMigrationTest::testAddColumnOperation": 0.427, "tests\\unit\\migrations\\ApprovalRegistrationMigrationTest::testCreateIndexOperation": 0, "tests\\unit\\models\\ApprovalRegistrationTest::testGetMaxRegistrationNumberByReformId": 0.252, "tests\\unit\\models\\ApprovalRegistrationTest::testGetMaxRegistrationNumberReturnsZeroWhenNoRegistrations": 0.002, "tests\\unit\\models\\ApprovalRegistrationTest::testAssignRegistrationNumberSkipsNonConfirmedApproval": 0.017, "tests\\unit\\models\\ApprovalRegistrationTest::testAssignRegistrationNumberSuccess": 0.152, "tests\\unit\\models\\ApprovalRegistrationTest::testAssignRegistrationNumberThrowsExceptionAfterMaxAttempts": 0.006, "tests\\unit\\models\\ApprovalRegistrationTest::testGetFormattedRegistrationNumber": 0.001, "tests\\unit\\models\\ApprovalRegistrationTest::testGetRegistrationDateTime": 0.001, "tests\\integration\\ApprovalRegistrationNumberDbTest::testCompleteApprovalWorkflow": 1.232, "tests\\integration\\ApprovalRegistrationNumberDbTest::testSequentialRegistrationNumbers": 0.065, "tests\\integration\\ApprovalRegistrationNumberDbTest::testRegistrationNumbersAcrossMultipleReforms": 0.034, "tests\\integration\\ApprovalRegistrationNumberDbTest::testRegistrationNumberUniqueness": 0.024, "tests\\integration\\ApprovalRegistrationNumberDbTest::testTransactionHandling": 0.024, "tests\\integration\\ApprovalRegistrationNumberDbTest::testRecoveryFromFailures": 0.024, "tests\\integration\\ApprovalSigningIntegrationTest::testSigningProcessWithRegistration": 0.374, "tests\\integration\\ApprovalSigningIntegrationTest::testSequentialSigning": 0, "tests\\integration\\ApprovalSigningIntegrationTest::testTransactionRollback": 0, "tests\\unit\\models\\ApprovalRegistrationTest::testAssignRegistrationNumberByNodeSuccess": 0.112, "tests\\unit\\services\\RegistrationNumberServiceTest::testSingletonInstance": 0.095, "tests\\unit\\services\\RegistrationNumberServiceTest::testGetNextAvailableNumber": 0.218, "tests\\unit\\services\\RegistrationNumberServiceTest::testReserveNumber": 0.111, "tests\\unit\\services\\RegistrationNumberServiceTest::testReserveAlreadyTakenNumber": 0.01, "tests\\unit\\services\\RegistrationNumberServiceTest::testConfirmReservedNumber": 0.101, "tests\\unit\\services\\RegistrationNumberServiceTest::testReleaseReservedNumber": 0.001, "tests\\unit\\services\\RegistrationNumberServiceTest::testGetMaxNumber": 0.002, "tests\\unit\\services\\RegistrationNumberServiceTest::testCleanupExpiredReservations": 0.025, "tests\\unit\\models\\ApprovalRegistrationNumberTest::testRepeatableSign": 23.143, "ApprovalRegistrationNumberTest::testGetMaxRegistrationNumberByReformId": 0.264, "frontend\\tests\\unit\\models\\ResetPasswordFormTest::testResetWrongToken": 0.145, "frontend\\tests\\unit\\models\\ResetPasswordFormTest::testResetCorrectToken": 0, "ApprovalRegistrationNumberTest::testSignOneFolder": 7.189, "ApprovalRegistrationNumberTest::testSignManyFolders": 6.552}}