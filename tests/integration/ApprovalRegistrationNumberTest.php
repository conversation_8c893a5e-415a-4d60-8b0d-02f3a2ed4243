<?php

use common\fixtures\ApprovalFolderFixture;
use common\fixtures\ApprovalFoldersFixture;
use common\fixtures\ReformFixture;
use common\fixtures\UserFixture;
use common\models\User;
use frontend\models\Approval;
use frontend\models\approval\ApprovalFolder;
use frontend\models\Models;
use PHPUnit\Framework\TestCase;
use yii\test\FixtureTrait;

class ApprovalRegistrationNumberTest extends TestCase
{

    use FixtureTrait;

    public function fixtures(): array
    {
        return [
            yii\test\InitDbFixture::class,
//            'approvalFolder' => ApprovalFolderFixture::class,
            'approvalFolders' => ApprovalFoldersFixture::class,
        ];
    }

    public function setUp(): void
    {
        parent::setUp();
        $this->unloadFixtures();
        $this->loadFixtures();
    }

    public function tearDown(): void
    {
//        $this->unloadFixtures();
//        parent::tearDown();
    }

    public function testSignOneFolder(): void
    {
        $user = User::findOne(2);

        Models::$log_enabled = false;

        if (!$user) {
            $this->markTestSkipped('No users available for testing');
        }

        Yii::$app->user->setIdentity($user);

        $approvalFolder = ApprovalFolder::find()->where(['id_author' => $user->id])->one();

        if (!$approvalFolder) {
            $this->markTestSkipped('No approval folders available for testing');
        }

        $maxNumber = Approval::getMaxRegistrationNumberByReformId(1);
        $this->assertEquals(0, $maxNumber, 'Max registration number should be 0 when no registrations exist');

        $result = $approvalFolder->sign();
        $this->assertTrue($result, 'Signing failed');

        $maxNumber = Approval::getMaxRegistrationNumberByReformId(1);
        $this->assertEquals(1, $maxNumber, 'Max registration number should be 0 when no registrations exist');
    }

    public function testSignManyFolders(): void
    {
        $approvalFolders = $this->getFixture('approvalFolders');
        $user = User::findOne(2);

        Models::$log_enabled = false;

        if (!$user) {
            $this->markTestSkipped('No users available for testing');
        }

        Yii::$app->user->setIdentity($user);

        $approvalFolder = ApprovalFolder::find()->where(['id_author' => $user->id])->one();

        if (!$approvalFolder) {
            $this->markTestSkipped('No approval folders available for testing');
        }

        $maxNumber = Approval::getMaxRegistrationNumberByReformId(1);
        $this->assertEquals(0, $maxNumber, 'Max registration number should be 0 when no registrations exist');

        $result = $approvalFolder->sign();
        $this->assertTrue($result, 'Signing failed');

        $maxNumber = Approval::getMaxRegistrationNumberByReformId(1);
        $this->assertEquals(1, $maxNumber, 'Max registration number should be 0 when no registrations exist');
    }
}