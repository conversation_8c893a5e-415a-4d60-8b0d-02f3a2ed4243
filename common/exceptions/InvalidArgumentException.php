<?php
declare(strict_types=1);

namespace common\exceptions;

use frontend\dto\ActiveLogMessageDto;
use common\helpers\Helper;
use yii\helpers\Url;
use Yii;

class InvalidArgumentException extends \BadMethodCallException
{
    use ExceptionTrait;

    public function __construct($message = null, $code = 0, \Exception $previous = null)
    {
        //$messageLog = $this->getMessageLog('argument', 'find', 'warning', 'Invalid argument');

        parent::__construct($message, $code, $previous);
    }
}
