/* FOOTER */
footer.footer {
    margin-top: -50px;
}
button.close {
    color: #1D273E !important;
    font-size: 26px;
}

/* TOP HEADER */

.row.title-row {
    position: sticky;
    top: 64px;
    z-index: 50;
    background: #f5f6fa;
    margin-right: -30px;
    margin-left: -30px;
    padding: 0 15px;
}

.row.title-row.top-sticky {
    top: 0;
}

.title-header-fake{
    /* height: 102px;  */
    background-color: #fff; 
    border-radius: 8px;
    display: flex;
    gap: 16px;
    padding-left: 16px;
    padding-right: 16px;
    padding-top: 12px;
    padding-bottom: 12px;
    align-items: center;
    min-height: 84px;
}


.title-header-fake.big{
    height: 102px;
}

.title-header-fake .fake-status{
    width: 96px;
    flex-shrink: 0;
}

.title-header-fake .fake-icon{
    width: 40px;
    flex-shrink: 0;
}

.title-header-fake .fake-title{
    flex-grow: 1;
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
}

.title-header-fake .fake-title h1{
    margin: 0px !important; /*  Rewrite rule */
    min-height: 0px; /*  Rewrite rule */
    font-weight: 600;
    font-size: 24px;
    line-height: 30px;
    color: #1D273E;
    display: block;
}

.title-header-fake .fake-title.sm-text{
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
}

.title-header-fake .fake-title.sm-text h1{
    font-size: 20px !important;
    line-height: 26px !important;
}

.title-header-fake .fake-buttons{
    flex-shrink: 0;
    padding-right: 4px;
    padding-left: 12px;
}

.header {
    background: #fff;
    border-bottom: solid 1px #d9dce2;
    position: fixed;
    z-index: 100;
    left: 0;
    width: 100%;
    top: 0;
    height: 64px;
}

.header-left {
    display: flex;
    align-items: center;
    margin-left: -15px;
    min-height: 64px;
}

.header-right {
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 64px;
}

.inner-header-right {
    display: flex;
    justify-content: end;
    max-width: 290px;
    width: 100%;
    margin-left: auto;
}

.inner-header-right .inner-header-item:first-of-type {
    padding: 0 10px 0 0;
}

.inner-header-right .inner-header-item {
    padding: 0 10px;
}

.inner-header-right .inner-header-item:last-of-type {
    padding: 0 0 0 10px;
}

.sidebar-btn-block {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 64px;
    min-height: 64px;
    border-right: solid 1px #d9dce2;
    cursor: pointer;
}

.sidebar-btn-block:hover {
    border-bottom: solid 1px #d9dce2;
    background-color: #f2f7ff;
}

.logo-block {
    margin-left: 16px;
}

.logo-block a {
    position: relative;
    top: 1px;
}

.logo-block a img {
    width: 173px;
    height: 24px;
}

/* Sidebar menu */

#sidebar-menu-block {
    width: 246px;
    height: 100%;
    padding: 64px 25px;
    background: #fff;
    position: fixed;
    top: 0;
    left: -246px;
    z-index: 99;
    box-shadow: 0 8px 18px 0 rgba(167, 169, 178, 0.39);
}

#sidebar-menu-block .title {
    margin-top: 30px;
    text-transform: uppercase;
    font-size: 10px;
    color: #8c93a6;
}

.main-menu {
    display: flex;
    flex-direction: column;
    padding: 10px 0;
}

.main-menu li {
    list-style: none;
    margin-bottom: 24px;
    display: flex;
    align-items: center;
}

.main-menu li img {
    margin-right: 15px;
}

.main-menu li a {
    color: #1d273e;
    font-size: 16px;
    font-family: 'Source Sans Pro', sans-serif;
    font-weight: 600;
}

.main-menu li.current-menu-item a, .main-menu li a:hover {
    color: #357fed;
}

/** NEW STYLE */
.grid-stack-widget-user_reform_pie .box-block .box-content.nicescroll {
    overflow: hidden;
}

.grid-stack-widget-user_reform_pie .box-block .box-content.nicescroll .highcharts-container {
    width: 100% !important;
}

.grid-stack-widget-user_reform_pie .box-block .box-content.nicescroll .highcharts-container svg {
    width: 100% !important;
}

.user-menu-block {
    /*margin-left: 20px !important;*/
}

.custom-tooltip {
    position: absolute;
    width: 200px;
    top: 0;
}

.custom-tooltip.left .tooltip-arrow {
    top: 50%;
    right: 0;
    margin-top: -5px;
    border-width: 5px 0 5px 5px;
    border-left-color: #1D273E;
}

.custom-tooltip.top .tooltip-arrow {
    bottom: 0;
    left: 50%;
    margin-left: -5px;
    border-width: 5px 5px 0;
    border-top-color: #1D273E;
}

.custom-tooltip.bottom .tooltip-arrow {
    top: 0;
    left: 50%;
    margin-left: -5px;
    border-width: 0 5px 5px;
    border-bottom-color: #1D273E;
}

.custom-tooltip.right .tooltip-arrow {
    top: 50%;
    left: 0;
    margin-top: -5px;
    border-width: 5px 5px 5px 0;
    border-right-color: #1D273E;
}

.modal-content {
    box-shadow: 0 6px 24px 0 #00000052;
    border: none;
    border-radius: 8px 8px 6px 6px;
}

.modal-content .modal-header {
    border-radius: 6px 6px 0 0 ;
}

.modal-dialog .table_block .thead:hover {
    cursor: pointer;
}

.modal .modal-drag-handle {
    cursor: move;
}

.modal-dialog .table_block :active {
    cursor: grabbing;
    cursor: -moz-grabbing;
    cursor: -webkit-grabbing;
}

/** NEW STYLE END */

/* Notifications */

.notifications-block {
    list-style: none;
    margin-bottom: 0;
}

.notifications-block > .notifications-menu .dropdown-toggle {
    border-radius: 50%;
    background: aliceblue;
    position: relative;
    width: 32px;
    height: 32px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 3px;
}

.notifications-block > .notifications-menu > a {
    color: #357fed;
}

.notifications-block > .notifications-menu .dropdown-toggle:hover {
    background-color: #357fed;
    color: #ffffff;
}

.notifications-block > .notifications-menu .dropdown-toggle:hover .notify-count {
    border: 1px solid #FFFFFF;
}

.notifications-block .notifications-menu .notify-count {
    padding: 4px;
    font-size: 8px;
    height: 16px;
    width: 16px;
    position: absolute;
    background: #357fed;
    border-radius: 50%;
    color: #fff;
    top: -3px;
    right: -3px;
    display: flex;
    justify-content: center;
    align-items: center;
    border: 1px solid #ffffff;
}

.notifications-block > .notifications-menu > .dropdown-menu {
    width: 380px;
}

.notifications-block > .notifications-menu > .dropdown-menu > li,
.notifications-block > .messages-menu > .dropdown-menu > li,
.notifications-block > .tasks-menu > .dropdown-menu > li {
    position: relative;
}

.notifications-block > .notifications-menu > .dropdown-menu > li.navbar-menu-header,
.notifications-block > .messages-menu > .dropdown-menu > li.navbar-menu-header,
.notifications-block > .tasks-menu > .dropdown-menu > li.navbar-menu-header {
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
    background-color: #ffffff;
    padding: 7px 10px;
    border-bottom: 1px solid #f4f4f4;
    color: #444444;
    font-size: 14px;
}

.notifications-block > .notifications-menu > .dropdown-menu > li.navbar-menu-footer > a,
.notifications-block > .messages-menu > .dropdown-menu > li.navbar-menu-footer > a,
.notifications-block > .tasks-menu > .dropdown-menu > li.navbar-menu-footer > a {
    border-top-left-radius: 0;
    border-top-right-radius: 0;
    border-bottom-right-radius: 4px;
    border-bottom-left-radius: 4px;
    font-size: 12px;
    background-color: #fff;
    padding: 7px 10px;
    border-bottom: 1px solid #eeeeee;
    color: #444 !important;
    text-align: center;
}

@media (max-width: 991px) {
    .notifications-block > .notifications-menu > .dropdown-menu > li.navbar-menu-footer > a,
    .notifications-block > .messages-menu > .dropdown-menu > li.navbar-menu-footer > a,
    .notifications-block > .tasks-menu > .dropdown-menu > li.navbar-menu-footer > a {
        background: #fff !important;
        color: #444 !important;
    }
}

.notifications-block > .notifications-menu > .dropdown-menu > li.navbar-menu-footer > a:hover,
.notifications-block > .messages-menu > .dropdown-menu > li.navbar-menu-footer > a:hover,
.notifications-block > .tasks-menu > .dropdown-menu > li.navbar-menu-footer > a:hover {
    text-decoration: none;
    font-weight: normal;
}

.notifications-block > .notifications-menu > .dropdown-menu > li .menu,
.notifications-block > .messages-menu > .dropdown-menu > li .menu,
.notifications-block > .tasks-menu > .dropdown-menu > li .menu {
    max-height: 200px;
    margin: 0;
    padding: 0;
    list-style: none;
    /*overflow-x: hidden;*/
}

.notifications-block > .notifications-menu > .dropdown-menu > li .menu > li > a,
.notifications-block > .messages-menu > .dropdown-menu > li .menu > li > a,
.notifications-block > .tasks-menu > .dropdown-menu > li .menu > li > a {
    display: block;
    white-space: nowrap;
    border-bottom: 1px solid #f4f4f4;
}

.notifications-block > .notifications-menu > .dropdown-menu > li .menu > li > a:hover,
.notifications-block > .messages-menu > .dropdown-menu > li .menu > li > a:hover,
.notifications-block > .tasks-menu > .dropdown-menu > li .menu > li > a:hover {
    background: #fcfcfc;
    text-decoration: none;
}

.notifications-block > .notifications-menu > .dropdown-menu > li .menu > li > a {
    color: #444444;
    overflow: hidden;
    text-overflow: ellipsis;
    padding: 10px;
}

.notifications-block > .notifications-menu > .dropdown-menu > li .menu > li > a > .glyphicon,
.notifications-block > .notifications-menu > .dropdown-menu > li .menu > li > a > .fa,
.notifications-block > .notifications-menu > .dropdown-menu > li .menu > li > a > .ion {
    width: 20px;
}

.page-visible-btn .badge {
    padding: 0 5px !important;
}

.search-form .mixitup-search-input {
    padding-left: 36px;
}

.sphere-block .btn-nav-wrap {
    right: 20px;
}

.mixitup-block .btn-nav-wrap {
    right: 9px;
    top: 41%;
    position: absolute;
}

.mixitup-block .xs-title .entity-status {
    padding: 2px 8px;
    border-radius: 2px;
    font-size: 10px;
    font-weight: 600;
    display: table;
    text-transform: uppercase;
}

.mixitup-block .xs-title .entity-status.color-draft {
    background-color: #ffe4da;
    color: #df5423;
}

.mixitup-block .xs-title .entity-status.color-for-approval {
    background-color: #ffe1b5;
    color: #df9423;
}

.mixitup-block .xs-title .entity-status.color-implementation {
    background-color: #f2f7ff;
    color: #357fed;
}

.mixitup-block .xs-title .info-request a {
    color: #1d273e;
}

.mixitup-block .xs-title .subtitle {
    display: flex;
    justify-content: space-between;
    width: 80%;
}

.mixitup-block .xs-title .subtitle div label {
    font-size: 10px;
    margin-bottom: 2px;
}

.mixitup-block .xs-title .subtitle div {
    display: flex;
    flex-direction: column;
}

.mixitup-block .xs-title .subtitle div span {
    font-size: 14px;
}

.pl-100 {
    padding-left: 100px !important;
}

.pr-100 {
    padding-right: 100px !important;
}
.pl-60 {
    padding-left: 60px !important;
}

.pr-60 {
    padding-right: 60px !important;
}

.pb-12 {
    padding-bottom: 12px !important;
}

.mb-0 {
    margin-bottom: 0 !important;
}

.mb-18 {
    margin-bottom: 18px !important;
}

.mb-20 {
    margin-bottom: 20px !important;
}

.mt-20 {
    margin-top: 20px !important;
}

.ml-20 {
    margin-left: 20px !important;
}

.mr-20 {
    margin-right: 20px !important;
}

.mb-32 {
    margin-bottom: 32px !important;
}

.mt-32 {
    margin-top: 32px !important;
}

.mb-48 {
    margin-bottom: 48px !important;
}

.mt-48 {
    margin-top: 48px !important;
}

.mb-4 {
    margin-bottom: 4px !important;
}

.mt-4 {
    margin-top: 4px !important;
}

.ml-4 {
    margin-left: 4px !important;
}

.mr-4 {
    margin-right: 4px !important;
}

.mb-10 {
    margin-bottom: 10px !important;
}

.mt-10 {
    margin-top: 10px !important;
}

.ml-10 {
    margin-left: 10px !important;
}

.mr-10 {
    margin-right: 10px !important;
}

.mb-5 {
    margin-bottom: 5px !important;
}

.mt-5 {
    margin-top: 5px !important;
}

.ml-5 {
    margin-left: 5px !important;
}

.mr-5 {
    margin-right: 5px !important;
}

.progress-bar.bg-success {
    background-color: #3c763d;
}

.progress-bar.bg-danger {
    background-color: #a94442;
}

.progress-bar.bg-warning {
    background-color: #8a6d3b;
}

.info-wrapper i {
    position: absolute;
    top: 15px;
    left: 15px;
    color: #357fed;
}

.info-wrapper {
    position: relative;
    background-color: #e1ecfd;
    margin: 16px 0 24px;
    padding: 10px 40px;
    border-radius: 4px;

}

.block-wrapper {
    padding: 10px;
    margin: 5px 15px;
    border: 1px solid #afafaf;
}

.global-goal {
    font-style: italic;
    font-size: 0.8em;
    background-color: #953b39;
    color: #fff;
    padding: 2px;
}

.goal {
    font-style: italic;
    font-size: 0.75em;
    background-color: #3f3e95;
    color: #fff;
    padding: 2px;
}

.sidebar .sidebar-load-place {
    height: 500px;
}

.sidebar {
    border-left: 1px solid #666;
    height: 100%;
}

.modal.in {
    background: #96969657;
}

.modal form textarea {
    resize: none;
    border-color: #d9dce2;
}

.modal-dialog.ui-draggable-dragging {
    cursor: move;
}

.modal .modal-dialog {
    border-radius: 4px;
    margin: 0px;
}

.modals .modal-wrapper{
    padding: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modals .modal-wrapper.no-background{
    background: none;
}

.modal button.close {
    position: absolute;
    top:20px;
    font-size: 17px;
    right:24px;
    opacity: 1;
    color: #fff !important;
}
.modal-header {
    padding: 16px 24px !important;
}
.modal-body {
    padding: 16px 24px 32px!important;
}
.modal.primary .modal-header {
    background-color: #357FED;
}
.modal.warning .modal-header {
    background-color: #FAC905;
}
.modal.danger .modal-header {
    background-color: #DF2323;
}
.modal.danger .modal-header h5,
.modal.primary .modal-header h5{
    color: #fff;
}
.modal.danger .modal-header button.close,
.modal.primary .modal-header button.close{
    color: #fff;
}
.modal.warning .modal-header button.close{
    color: #1d273e !important;
    text-shadow: 0 1px 0 #1d273e !important;
}
.modal-footer {
    border-top:none!important;
}


.modal-dialog {
    margin-top: -20px;
    width: 50%;
}

.modal_lg .modal-dialog {
    width: 768px;
}

.modal_sm .modal-dialog {
    width: 384px;
}

.modal_xl .modal-dialog {
    width: 1024px;
}

.modal_m .modal-dialog {
    width: 512px;
}


.reform-row {
    border: 1px solid #666;
    padding: 10px;
    margin: 10px;
}

/*.btn-add{*/
/*bottom:-50px !important;*/
/*}*/
.btn {
    box-shadow: none !important;
}

.colap-up.collapsed:hover {
    opacity: 1 !important;
    background-color: #5c9eed;
    border-color: #67a2ed;
    color: #fff;
}

.spoiler.panel-group.collapse .panel-heading {
    margin-bottom: 5px;
}

.spoiler.panel-group.collapse {
    margin-bottom: 0;
    border: none;
}

.spoiler.panel-group.collapse .panel-body {
    padding: 0;
    border: none;
}

.spoiler.panel-group.collapse .panel-heading,
.spoiler.panel-group.collapse .panel {
    padding: 0;
    border: none;
    box-shadow: none;
    background: none;
}

.spoiler.panel-group.collapse .panel-heading a span {
    color: #357FED;
    border: none;
    border-bottom: 1px dashed #357FED;
    text-decoration: none;
    font-size: 14px;
    font-weight: normal;
}

.spoiler.panel-group.collapse .panel-heading .collapse-toggle:after {
    content: "\f106";
    font-size: 13px;
    font-family: FontAwesome;
    margin-left: 5px;
    color: #357FED;
}

.spoiler.panel-group.collapse .panel-heading .collapse-toggle {
    display: none;
}

.spoiler.panel-group.collapse .panel-heading .collapse-toggle.collapsed {
    display: block;
}

.spoiler.panel-group.collapse .panel-heading .collapse-toggle.collapsed:after {
    content: "\f107" !important;
    position: relative;
    top: 2px;
}

input.text-end {
    text-align: right;
}

.hierarchy-result-wrapper .content-hovered{
    transition: .2s;
    opacity: 0;
}

.hierarchy-result-wrapper .table-block:hover .content-hovered{
    opacity: 100;
}

.reform-view {
    width: 100%;
    clear: both;
}

.hierarchy-cell__body > .ultimate-goal-name{
    display: inline-flex;
    align-items: center;
    font-size: 18px;
    font-weight: bold;
}
.hierarchy-cell p{
    margin: 0;
}

.hierarchy-cell .menu-element{
    text-transform: uppercase;
}

.hierarchy-cell {
    position: relative;
    border-radius: 4px;
    min-width: 141px;
    border: 2px solid #D9DCE2;
    padding: 12px;
    background: #FFF;
    box-shadow: 0px 5px 13px 0px #D2D4E9;
    max-height: 250px;
    display: flex;
    flex-direction: column;
}

.hierarchy-cell-header {
    display: flex;
    justify-content: space-between;
    line-height: 14px;
}

.hierarchy-cell-header_show-hover{
    transition: .2s;
    opacity: 0;
}

.hierarchy-cell:hover .hierarchy-cell-header_show-hover{
    opacity: 100;
}

.hierarchy-cell-header__title {
    display: inline-block;
    color: #8C93A6;
    font-size: 10px;
    font-style: normal;
    font-weight: 700;
    line-height: 14px;
    margin: 0;
    text-transform: uppercase;
}

.hierarchy-cell-header__button{
    font-size: 14px;
    line-height: 14px;
}

.hierarchy-cell__body {
    padding-top: 10px;
    color: #357FED;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    overflow: hidden;

    max-width: calc(100vw - 114px);
    position: sticky;
    left: 6px;
    right: 6px;
}

.list-outputs{
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.load-more{
    cursor: pointer;
    display: flex;
    flex-direction: column;
    padding: 8px 16px;
    border-radius: 4px;
    background: #FFF;
    box-shadow: 0px 4px 16px 0px rgba(204, 207, 221, 0.30);
    color: var(--primary-600, #357FED);
    text-align: center;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 18px;
}

.load-more span{
    display: block;
    margin: 0px auto;
    text-decoration: underline;
    text-decoration-style: dashed;
    text-underline-offset: 2px;
}

/* .load-more span::after{
    content: '';
    display: block;
    width: 100%;
    height: 1px;
    background: linear-gradient(to right, transparent 0px, transparent 2px, #357FED 2px, #357FED 6px, transparent 6px, transparent 8px);
    background-repeat: repeat;
    background-position: center;
    background-size: 8px 1px;
} */

.table-block {
    border-radius: 4px;
    box-shadow: 0 5px 13px 0 #d2d4e9;
}

.table-block .block {
    background: #fff;
}

.table-block .block table thead tr th {
    /*position: sticky;*/
    /*top: 116px;*/
    z-index: 10;
    height: 57px;
    padding: 15px 10px;
    text-transform: uppercase;
    background: #f4f6f9;
    border: 0;
    font-size: 10px;
    font-weight: 600;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    color: #1d273e;
    vertical-align: middle;
}

.table-block .block table tbody tr {
    border-bottom: 1px solid #d9dce2;
}

.table-block .block table tbody tr td {
    border: none;
}

.table-block .block table tbody tr td .highlight {
    color: #357fed;
}

.table-block .block table tbody .block-line .block-label {
    font-size: 14px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    color: #A4B1C8;
}

.table-block .block table tbody tr td .regulator span {
    padding: 2px 8px 3px;
    border-radius: 2px;
    background-color: #f2f7ff;
    font-size: 10px;
    font-weight: 600;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    color: #357fed;
    text-transform: uppercase;
    display: block;
    width: max-content;
    width: -moz-max-content;
    margin-top: 5px;
}

.table-block .block table tbody tr .total-difference {
    font-weight: bold;
}

.table-block .block table tbody tr .total-difference.red {
    color: #df5423;
}

.table-block .block table tbody tr .total-difference.green {
    color: #18cc58;
}
.intent .block-intent-nav-2 {
    min-height: 32px;
    margin-top: 2px;
    margin-bottom: 6px;
}

.list-method-view .block-table {
    width: 69%;
}

.list-method-view .block {
    margin-bottom: 22px;
}

@media (max-width: 1199px) {
    .list-method-view .block-table {
        width: 100%;
    }

    .navbar-toggle {
        display: block;
    }

    .navbar-collapse.collapse {
        display: none !important;
        overflow: auto !important;
    }

    .collapse.in {
        display: block !important;
        /* box-shadow: 0 12px 23px 0 rgba(167, 169, 178, 0.55); */
    }
}

@media (max-width: 991px) {
    .list-method-view .block {
        margin-bottom: 20px;
    }
}

a.disabled {
    color: #929292 !important;
    text-decoration: none !important;
    cursor: not-allowed;
}

.sort-block .sort-label.active {
    color: #2be56d;
}

.sort-block .sort-label.active .btn-carret {
    border-top-color: #2be56d;
    display: inline-block;
}

.sort-block .sort-label .btn-carret {
    display: none;
}

.sort-block .sort-label .wrapper-btn-carret {
    display: inline-block;
    width: 4px;
}

.sort-block .sort-label.active.sort-reverse .btn-carret {
    transform: rotate(180deg);
}

.page-title-wrap .header-icon {
    display: inline-block;
    width: 48px;
    height: 48px;
    margin: 6px 12px 0 0;
    padding: 13px 0;
    color: #ffffff;
    background-color: #357fed;
    border-radius: 50%;
    -webkit-border-radius: 50%;
    float: left;
    text-align: center;
    font-size: 20px;
}

.page-title-wrap.background-color h1 span {
    color: #fff;
}

.page-title-wrap.background-color.none h1 span,
.page-title-wrap.background-color.white h1 span {
    color: #1d273e;
}

.header-right .language-selector,
.auth-block .language-selector {
    display: inline-block;
    font-size: 10px;
    position: relative;
    right: -8px;
}

.header-right .language-selector ul li,
.auth-block .language-selector ul li {
    cursor: pointer;
    list-style: none !important;
}

.header-right .language-selector ul li:hover,
.auth-block .language-selector ul li:hover {
    color: #357fed !important;
}

.header-right .language-selector ul li.active,
.auth-block .language-selector ul li.active {
    color: #357fed !important;
}

/** NEW STYLE */
.login-block {
    margin-left: 70px;
    text-align: right;
}

.login-block a {
    height: 100%;
    padding-top: 11px;
    border-radius: 4px;
    background-color: #357fed;
    color: #ffffff !important;
    border: 1px solid #357fed;
}

.login-block a:hover {
    color: #357fed !important;
    border: 1px solid #f2f7ff;
    background-color: #f2f7ff;
}

/** NEW STYLE END */

/*
table.table, table.table td, table.table th {
    border: solid 1px #d9dce2;
}
*/
.block-table.indicator-table {
    /*display: flex !important;*/
}

.block-table.indicator-table.reform-list .block-table-inner {
    padding: 10px;
    float: left !important;
    /*width: 16.666666%;*/
}

@media (max-width: 767px) {
    .block-table.indicator-table .block-table-inner {
        width: 100%;
    }

    .reform-widget-wrapper {
        height: 40px !important;
        margin-bottom: 10px !important;
    }

    ul.mixitup-wrapper-tabs li {
        width: 100% !important;
    }

    .logo_block img {
        height: 35px !important;
    }

    .user-nav .dropdown-menu {
        right: -135% !important;
        min-width: 250px !important;
    }

    .problem-block {
        display: block !important;
    }
}

.btn-star.checked:before {
    content: '\e843';
    color: #f4d949;
}

.btn-perspective {
    -webkit-transform: perspective(1px);
    -moz-transform: perspective(1px);
    -ms-transform: perspective(1px);
    -o-transform: perspective(1px);
    transform: perspective(1px);
}

/*.table-bg-progress {*/
/*z-index: 0;*/
/*}*/
/*.goal-name{*/
/*z-index: 99;*/
/*}*/

.page-section.fullscreen {
    padding: 0;
}

.doc-status-nav .btn {
    border-bottom: 2px solid #f5f6fa !important;
    border-top: none;
    border-left: none;
    border-right: none;
    border-radius: 0 !important;
    -webkit-transition: border 0.3s;
    -moz-transition: border 0.3s;
    -ms-transition: border 0.3s;
    -o-transition: border 0.3s;
    transition: border 0.3s;
}

.doc-status-nav .btn.active, .doc-status-nav .btn:hover {
    background-color: #f5f6fa !important;
    border-top: none;
    border-left: none;
    border-right: none;
    border-bottom: 2px solid #357fed !important;
    text-decoration: none;
}

.reform-icon {
    position: absolute;
    top: 12px;
    color: #8c93a6;
    /*left:10px;*/
}

.favorite_button_top button.btn-star {
    box-shadow: 0 7px 14px 0 rgb(204 207 221 / 30%);
    border-radius: 50%;
    width: 30px;
    height: 30px;
}

.icon-title .btn-star {
    position: absolute;
    top: -3px !important;
    left: inherit !important;
    right: 13px !important;
    z-index: 1;
    background: #fff;
    border-radius: 50%;
    width: 30px;
    height: 30px;
}

.mixitup-wrapper .btn-info:hover {
    color: #357fed;
}

.mixitup-wrapper .btn-info {
    position: absolute;
    top: 100px !important;
    left: inherit !important;
    right: 8px !important;
    z-index: 1;
    background: #fff;
    text-align: center;
    color: #1d273e;
    border-radius: 50%;
    width: 30px;
    height: 30px;
}

.btn-star:hover {
    color: #357fed;
    background-color: #f5f6fa;
}

.icon-title .btn-i:before {
    font-family: "Icons";
    margin: -4px;
    font-size: 12px;
    content: '\e84b';
}

.icon-title .btn-i:hover {
    color: #357fed;
    background-color: #f5f6fa;
}

.icon-title .btn-i {
    position: absolute;
    bottom: -25px !important;
    left: inherit !important;
    right: 0 !important;
    z-index: 1;
    background: #fff;
    border-radius: 50%;
    width: 30px;
    height: 30px;
}

.legacy-title-row h1 {
    margin: 10px 0 !important;
}

.btn-custom span {
    text-decoration: underline;
}

.btn-custom.update span {
    text-decoration: none;
}

.btn-custom {
    border-radius: 4px;
    font-size: 10px;
    font-weight: bold;
    padding: 7px 12px;
    text-transform: uppercase;
    text-decoration: none;
    position: relative;
    overflow: hidden;
    display: inline-block;
    margin-bottom: 0;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    touch-action: manipulation;
    cursor: pointer;
    border: 1px solid transparent;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    line-height: 1.42857143;
    color: #1d273e;
    background: transparent;
}

.btn-custom:hover, .btn-custom.active {
    color: #357fed;
    background-color: #fff;
    border-color: #d9dce2;
}

.btn-custom.update:hover {
    background-color: #e6e6e6;
    border-color: #adadad;
}

.reform-view .page-visible-btn a:hover {
    text-decoration: none;
}

.doc-status-nav {
    border: none !important;
}

.btn-nav-small:before {
    font-family: 'Icons';
    content: '\e800';
}

.mixitup-block .btn-nav-wrap {
    z-index: auto !important;
}

.btn-nav-small {
    position: relative;
    cursor: pointer;
    font-size: 14px;
}

.btn-nav-wrap-small {
    top: 3px !important;
}

.btn-nav-wrap-small ul li a {
    font-size: 10px !important;
}

.btn-nav-wrap-small ul li {
    list-style: none;
    margin: 0 10px;
}

.xs-title div {
    font-size: 10px !important;
}

.xs-title {
    font-weight: normal !important;
    margin-bottom: 0 !important;
}

.block-intent-title {
    max-width: 240px;
    overflow: hidden !important;
}

.text-ellipsis {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.text-ellipsis-2 {
    display: block;
    display: -webkit-box;
    line-clamp: 2;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

.no-overflow {
    overflow-x: unset !important;
}


.button-arrow-wrapper, .add-new-strategic-goal-wrapper {
    -webkit-transition: .3s all;
    -moz-transition: .3s all;
    -ms-transition: .3s all;
    -o-transition: .3s all;
    transition: .3s all;
}

.button-arrow-wrapper, .add-new-strategic-goal-wrapper, .add-new-operational-goal-wrapper {
    background-color: #f2f7ff;
    border-color: #f2f7ff;
    color: #357fed;
}

.button-arrow-wrapper:hover,
.add-new-strategic-goal-wrapper:hover {
    background-color: #d4e5ff;
    color: #357fed;
    cursor: pointer;
}

.button-arrow-wrapper {
    width: 30px;
    height: 30px;
    border-radius: 4px;
    display: inline-block;
    text-align: center;
    line-height: 28px;
    text-decoration: none;
    -webkit-transition: .3s all;
    -moz-transition: .3s all;
    -ms-transition: .3s all;
    -o-transition: .3s all;
    transition: .3s all;
    padding: 0;
}

.button-arrow-wrapper .button-arrow {
    margin: 0;
}

.button-arrow-wrapper .icon-trash-empty:before {
    font-size: 18px;
    line-height: 28px;
}

.button-arrow-wrapper .button-arrow {
    font-weight: normal;
    /*margin-right: 9px;*/
    font-style: normal;
    display: inline-block;
    vertical-align: middle;
}

.button-arrow-wrapper .button-arrow::before {
    content: '\e838';
    -webkit-transform: rotate(180deg);
    -moz-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    -o-transform: rotate(180deg);
    transform: rotate(180deg);
    font-size: 12px;
}

.button-arrow-wrapper .button-arrow::before, .button-arrow-wrapper .button-arrow::before {
    font-family: "Icons";
    font-style: normal;
    font-weight: normal;
    speak: none;
    display: inline-block;
    text-decoration: inherit;
    text-align: center;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
}

.next-button-arrow {
    transform: rotate(180deg);
    margin-right: 5px;
}

.next-button-arrow-wrapper {
    opacity: 0;
}

.next-button-arrow-wrapper.show {
    opacity: 1;
}

.prev-button-arrow-wrapper {
    transform: translate(40px, -100%);
}

.strategic-goal-form .next-button-arrow-wrapper {
    position: absolute;
    display: none;
    opacity: 0;
    transform: translate(80px, -200%);
}

.strategic-goal-form .next-button-arrow-wrapper.show {
    display: block;
    opacity: 1;
    /*transform: translate(50px, -100%);*/
}

.operational-goal-form .next-button-arrow-wrapper {
    position: absolute;
    transform: translate(80px, -200%);
}

.help-block-error-reform,
.help-block-error-email {
    display: none;
}

.reset-button {
    width: 100px !important;
    right: 120px;
    bottom: 0;
    position: absolute !important;
    padding: 6px 25px !important;
}

.goal-description {
    display: none;
    /*transform: translateY(290px);*/
}

.reform-description {
    /*transform: translateY(150px);*/
}

.area-description {
    /*transform: translateY(25px);*/
}

.finish-button.show {
    display: block;
}

.finish-button {
    display: none;
}


.reform-table-view-wrapper {
    min-height: 250px;
}

table.reform-table-view td.arrow-cell,
table.reform-table-view tr.arrow-row {
    border: none !important;
}

.nav-menu {
    padding: 5px 0 10px;
}

.doc-status-nav li a {
    min-height: 40px;
}

.reform-table-view .fa-arrow-up {
    font-size: 10px;
    color: #8c93a6 !important;
}

.btn-pause {
    background-color: #fff !important;
}

ul.doc-status-nav li {
    border-left: 2px #eee solid !important;

}

ul.doc-status-nav li:first-child {
    border-left: none !important;
}

.doc-status-nav a.active {
    font-weight: bold !important;
}

.doc-status-nav a {
    font-weight: 600 !important;
    text-decoration: none !important;
}

.badge {
    background-color: #fff !important;
    color: #1d273e;
    border: 1px solid #d9dce2;
    font-size: 10px;
}

/*
.logo_block {
    margin-top: -5px;
}
*/

.nav-menu {
    box-shadow: -7px 0 20px 0 rgba(204, 207, 221, 0.5);
}

.shadow-wrapper {
    /*margin-top: 5px;
    -webkit-box-shadow: inset 0 7px 14px 0 rgba(119, 121, 134, 0.5);
    -moz-box-shadow: inset 0 7px 14px 0 rgba(119, 121, 134, 0.5);
    box-shadow: inset 0 7px 14px 0 rgba(119, 121, 134, 0.5);*/
    /*overflow-y: clip;*/
    /*height: 100%;*/
    /*width: 100%;*/
    /*position: relative;*/
}

/* Hide scrollbar for Chrome, Safari and Opera */
.shadow-wrapper::-webkit-scrollbar {
    display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */
.shadow-wrapper {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
}

/*
.shadow-outer-wrapper {
    margin-top: 5px;
    overflow: auto;
    height: 100%;
    width: 100%;
    position: relative;
    overflow-x: hidden;
}
*/
.table-body-progress {
    -webkit-transition: .3s all;
    -moz-transition: .3s all;
    -ms-transition: .3s all;
    -o-transition: .3s all;
    transition: .3s all;
    position: absolute;
    height: 0;
    opacity: 0.1;
    bottom: 0;
    border-radius: 5px;
    width: 100%;
}

.dropdown-menu a {
    text-decoration: none !important;
}

.dropdown-menu .menu-element:hover {
    color: #1d273e;
    background-color: #f5f6fa;
    border-color: #f5f6fa;
}

.dropdown-menu .menu-element.multiline {
    border: solid 1px #eceff5;
}

.dropdown-menu .menu-element {
    /*border: solid 1px #eceff5;*/
    display: block;
    /*margin: 3px 0;*/
    padding: 12px;
    border-radius: 4px;
    -webkit-transition: .3s box-shadow, .3s all;
    -moz-transition: .3s box-shadow, .3s all;
    -ms-transition: .3s box-shadow, .3s all;
    -o-transition: .3s box-shadow, .3s all;
    transition: .3s box-shadow, .3s all;
}

.dropdown-menu .menu-element > i {
    margin-right: 7px;
}

.btn-nav-wrap button {
    z-index: 5;
}

.btn-nav-wrap-small {
    padding: 0 5px;
}

.btn-nav-wrap-small .menu-element {
    font-size: 10px !important;
    margin: 2px 5px;

}

.intent-cell .block-intent-nav-2 .btn {
    position: inherit !important;
}

.switcher-asc-desc {
    transform: rotate(180deg);
    font-size: 6px !important;
    line-height: 0;
}

.switcher-asc-desc.sort-reverse {
    transform: rotate(0deg);
}

.reform-table-view td:hover .table-body-progress-value {
    color: rgba(225, 225, 225, 0.93);
}

/*.table-view-td {*/
/*    min-height: 130px;*/
/*}*/

table.table-fullscreen .table-view-goal-td .table-body {
    position: relative;
    height: 110px;
}

table.table-fullscreen .table-view-goal-td {
    height: 120px;
}

.table-view-task-td .table-body {
    position: relative;
    height: 100px;
}

.table-fullscreen .table-view-task-td .table-body {
    position: relative;
    height: 90px;
}

.table-view-task-td .table-block {
    display: block;
    height: 150px;
}

.table-fullscreen .table-view-td .table-block {
    position: relative;
}

.table-view-task-td {
    height: 150px;
}

table.table-fullscreen .text-td {
    padding: 2px !important;
}

table.table-fullscreen .text-td label {
    margin: 0;
    font-size: 8px;
}

table.reform-table-view.table-fullscreen td {
    min-width: 70px;
}

table.table-fullscreen {
    transition: 0.3s width;
}

table.table-fullscreen .table-view-goal-td .table-body {
    position: relative;
    min-height: 100px;
    overflow: hidden;
    font-size: 10px;
}

table.table-fullscreen .table-view-goal-td {
    padding: 3px;
}

table.table-fullscreen .table-view-goal-td .table-body .table-title {
    padding: 0px 2px;
}

table.table-fullscreen .table-view-task-td .table-body {
    position: relative;
    min-height: 50px;
}

table.table-fullscreen .table-view-task-td {
    height: 100px;
}

.table-view-td .reform-table-overflow {
    height: 30px;
    width: 100%;
    position: absolute;
    bottom: 0;
    left: 0;
    text-align: center;
    padding-top: 8px;
    cursor: help;
}

.sort-wrap ul li {

    color: #1d273e;
    /*text-decoration: underline;*/
}

.sort-wrap {
    font-size: 14px;
    font-weight: normal;
    display: inline-block;
}

.sort-wrap .sort-default:hover {
    color: #357fed;

}

.sort-wrap .sort-default {
    cursor: pointer;
}

.sort-wrap .dropdown-menu {
    padding: 0px;
    margin: 4px 0 0;
    min-width: 204px;
    right: -3px;
    left: auto;
}

.sort-wrap .dropdown-menu ul {
    list-style: none;
    margin: 0;
}

.sort-wrap .dropdown-menu ul li:hover {
    color: #357fed;
}

.sort-wrap .dropdown-menu ul li {
    cursor: pointer;
    /*margin-bottom: 13px;*/
    color: #1d273e;
}

.legend-button {
    position: absolute !important;
    bottom: 5px;
    left: 25px;
}

.legend.expanded {
    opacity: 1;
    width: 320px;
    height: 160px;

}

.legend button.close,
.legend .legend-text {
    display: none;
    transition: 0.5s all;
}

.legend.expanded button.close {
    display: block;
    position: relative;
    z-index: 2;
}

.legend.expanded .legend-text.active {
    display: none;
    padding: 15px;
    border-radius: 4px;
    background: rgba(255, 255, 255, 1);
    /* position: relative; */
    /* padding: 15px; */
    border: 1px solid #c4c4c4 !important;
    z-index: 1;
    position: relative;
}

.legend {
    opacity: 0.4;
    display: none !important;
    border: none;
    background: none;
    transition: .3s all;
    width: 90px;
    height: 30px;
    padding: 10px 20px;
    position: fixed;
    bottom: 30px;
    left: 60px;
}

.btn-default:focus, .btn-default:hover {
    background-color: #f2f7ff !important;
}

.legend-block {
    opacity: 0.2;
    margin-right: 10px;
    float: left;
    height: 15px;
    width: 15px;
}

.menu-add-reform {
    transition: 0.3s all;
    /*height:80px;*/
    /*width:150px;*/
    /*position: fixed;*/
    top: auto;
    bottom: 50px;
!important;
    /*right: 30px;*/
    /*z-index: 5;*/
}

.create_reform_wizard_1 .modal-dialog {
    width: 800px !important;
}

/*
.shadow-left {
    position: absolute;
    width: 10px;
    height: 100%;
    z-index: 2;
    box-shadow: 0 7px 14px 0 rgba(119, 121, 134, 0.5);
    transform: translateX(-10px);
}

.shadow-right {
    position: absolute;
    width: 10px;
    height: 100%;
    z-index: 2;
    box-shadow: 0 7px 14px 0 rgba(119, 121, 134, 0.5);
    right: -10px;
}
*/
.goal-description {
    font-size: 12px;
    padding: 10px;
    font-style: italic;
}

.btn-nav-wrap .dropdown-menu {
    padding: 1px !important;
}

.zoom-panel .zoom-btn i {
    margin: 0;
}

.modal-dialog .table_title {
    margin-top: 2px;
}

.table-view-td .goal-name-2,
.table-view-td .goal-name-2 a {
    font-size: 16px;
}

/*.form-group.last,*/
/*.form-group:last-child{*/
/*    margin-bottom: 5px !important;*/
/*    margin-top: 15px;*/
/*}*/
button.btn.btn-large {
    /*padding: 15px;*/
    font-size: 12px;
}

@media (min-width: 992px) {
    .animate {
        animation-duration: 0.3s;
        -webkit-animation-duration: 0.3s;
        animation-fill-mode: both;
        -webkit-animation-fill-mode: both;
    }
}

@keyframes slideIn {
    0% {
        transform: translateY(1rem);
        opacity: 0;
    }
    100% {
        transform: translateY(0rem);
        opacity: 1;
    }
    0% {
        transform: translateY(1rem);
        opacity: 0;
    }
}

@-webkit-keyframes slideIn {
    0% {
        -webkit-transform: transform;
        -webkit-opacity: 0;
    }
    100% {
        -webkit-transform: translateY(0);
        -webkit-opacity: 1;
    }
    0% {
        -webkit-transform: translateY(1rem);
        -webkit-opacity: 0;
    }
}

.slideIn {
    -webkit-animation-name: slideIn;
    animation-name: slideIn;
}

.progress-widget .progress {
    height: 5px !important;
    margin-top: 5px;
}

.reform-widget-wrapper {
    position: relative;
    min-height: 100px;
}

.reform-widget-wrapper .progress-widget {
    position: absolute;
    bottom: 5px;
    left: 0;
    right: 0;
    padding: 0 10px;
}

.reform-widget-wrapper .btn-nav {
    visibility: hidden;
    opacity: 0;
    -webkit-transition: .3s all;
    -moz-transition: .3s all;
    -ms-transition: .3s all;
    -o-transition: .3s all;
    transition: .3s all;
}

.reform-widget-wrapper:hover .btn-nav {
    visibility: visible;
    opacity: 1;
}

.zoom-3 .block-intent-title a {
    font-size: 10px;
}

.zoom-3 .block-description {
    height: 27px !important;
    overflow: hidden !important;
}

.zoom-2 .block-intent-title {
    /* height: 110px !important; */
}

.zoom-3 .block-intent-title {
    height: 27px !important;
}

.reform-block.reform-custom .reform-widget-wrapper {
    background-color: #fafafa;
}

.reform-widget-wrapper .btn-nav {
    height: 24px;
    width: 24px;
    font-size: 10px;
    position: absolute;
    top: -1px;
    right: -3px;
}

.opened_menu .menu-add-reform.dropdown-menu {
    right: 300px;
}

.ui-autocomplete {
    z-index: 2147483647 !important;
}

.btn-nav-wrap-small .dropdown-menu.last {
    left: -110px;
}

.table-fullscreen .ultimate-cell {
    padding: 4px !important;
}

.mixitup-wrapper .reform-widget-wrapper {
    height: 60px;
}

.mixitup-wrapper .reform-widget-wrapper:nth-last-child(-n+1) {

}

.mixitup-wrapper .block {
    background: #f5f8fc;
    box-shadow: 0 7px 17px 0 rgba(204, 207, 221, 0.3);
    display: flex;
}

.mixitup-wrapper .sphere-block {
    padding: 22px 23px;
    min-height: 243px;
}

.mixitup-wrapper .sphere-block {
    display: block;
    background: #ffffff;
}

.mixitup-wrapper .favorite-button-list-block {
    position: absolute;
    right: -5px;
    top: 20px;
}

.mixitup-wrapper .info-btn-list-block {
    position: absolute;
    right: 0;
    top: -13px;
}

.mixitup-wrapper .info-btn-list-block .ajax-request-link.btn-info {
    padding: 5px;
}

.list-spheres-view .mixitup-wrapper .favorite-button-list-block {
    right: 30px !important;
}

.mixitup-wrapper .info-button-list-block {
    position: absolute;
    right: 8px;
    top: 90px;
}

.input-group-addon {
    border: 1px solid #ddd;;
    background-color: #f5f6fa !important;
}

.input-group-addon-white .input-group-addon span {
    color: #A4B1C8;
    font-size: 12px;
}

.input-group-addon-white .input-group-addon i {
    color: #357FED;
    padding: 0px 7px !important;
    background-color: #F2F7FF;
}

.input-group-addon-white .input-group-addon {
    border-left: none !important;
    background-color: #fff !important;
    padding: 12px 12px 12px 8px;
}

.input-group.input-group-addon-white .form-control:first-child {
    border-right: 0;
    padding: 12px 0px 12px 12px;
}

.form-control-no-bg {
    background-image: none !important;
}


a:hover, a:focus {
    text-decoration: none !important;
}

.page-title .page-title-h1 .page-title-icon {
    color: #357fed;
    font-size: 0.9em;
    top: 2px;
    position: relative;
    padding-right: 5px;
    z-index: 90;
}

.page-title .page-title-h1 {
    /*margin: 10px 0 0 0 !important;*/
    /*float: left;*/
}

.page-title-wrap.background-color {
    margin: 8px 0;
    border-radius: 8px;
}

.page-title-wrap.background-color.none {
    margin: 0;
}

.page-title-wrap.background-color h1 a {
    color: #fff;
}

.page-title-wrap.background-color.white h1 a {
    color: #1D273E;
}

.page-title-wrap .background-image .circle-block {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    margin-right: 10px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.page-title-wrap .background-image .circle-block i {
    /*width: 29%;*/
    font-size: 18px;
    margin-left: 1px;
}

.page-title-wrap .background-image .circle-block i.white {
    color: #fff;
}

.page-title-wrap .background-image .circle-block i.blue {
    color: #381ce2;
}

.page-title-wrap .background-image .circle-block i.orange {
    color: #dc3b3b;
}

.page-title-wrap .background-image .circle-block i.green {
    color: #239dbc;
}

.page-title-wrap .background-image .circle-block i.ocean {
    color: #6078ea;
}

.page-title-wrap .background-image .circle-block i.red {
    color: #a31d1d;
}

.page-title-wrap .background-image .circle-block.white {
    background: #fff;
}

.page-title-wrap .background-image-bottom {
    position: absolute;
    width: 100%;
    bottom: 0px;
    margin-left: -15px;
}

.page-title .page-title-h1.background-image {
    height: 70px;
    display: flex;
    align-items: center;
    background-repeat: no-repeat;
    background-position-x: right;
    background-position: 90% -35px;
    margin: 0 !important;
    color: #fff;
    padding: 0px 12px;
}

.page-title-wrap .background-image .circle-block.blue {
    background-image: linear-gradient(315deg, #e6717a, #381ce2 1%);
}

.page-title-wrap .background-image .circle-block.blue-default {
    background: #357fed;
}

.page-title-wrap .background-image .circle-block.orange {
    background-image: linear-gradient(135deg, #dc3b3b, #db9526);
}

.page-title-wrap .background-image .circle-block.green {
    background-image: linear-gradient(135deg, #239dbc, #84d914);
}

.page-title-wrap .background-image .circle-block.ocean {
    background-image: linear-gradient(315deg, #32eeb3 99%, #6078ea 1%);
}

.page-title-wrap .background-image .circle-block.red {
    background-image: linear-gradient(315deg, #e70fd9, #a31d1d);
}

.page-title-wrap.background-color.blue {
    background-image: linear-gradient(304deg, #e35c67, #381ce2 100%);
}

.page-title-wrap.background-color.orange {
    background-image: linear-gradient(124deg, #dc3b3b, #db9526 100%);
}

.page-title-wrap.background-color.green {
    background-image: linear-gradient(124deg, #239dbc, #84d914 100%);
}

.page-title-wrap.background-color.ocean {
    background-image: linear-gradient(124deg, #6078ea, #32eeb3 100%);
}

.page-title-wrap.background-color.red {
    background-image: linear-gradient(124deg, #a31d1d, #e70fd9 100%);
}

.page-title-wrap.background-color.white {
    background-color: #fff;
}

.page-title-wrap.background-color.none {
    background-color: transparent;
    padding: 0px;
}

.page-title-wrap.background-color.white .page-title-h1 {
    color: #1D273E;
}

.page-title-wrap.background-color.none .page-title-h1 {
    color: #1D273E;
}

.page-title > label {
    /*position: relative !important;*/
    /*margin-top: 5px;*/
    /*font-style: italic;*/
}

.page-body {
    padding-bottom: 50px;
}

.field-uploadform-docfile .file-preview {
    border: none !important;
    margin: 0 !important;
    padding: 0 !important;
}

.field-uploadform-docfile .file-input {
    border-radius: 5px;
    border: 1px solid rgb(221, 221, 221);
    padding: 8px;
    width: 100%;
    margin-bottom: 5px;
}

.field-reform-date_finish .help-block {
    margin-top: 80px;
    padding: 10px 0;
}

.widget-text {
    font-size: 0.5em;
    display: block;
}

.intent .btn-nav {
    /*position: absolute;*/
    right: 10px;
}

.indicator-table-period {
    border: 0;
    background: transparent;
    margin-top: 5px;
    margin-left: 5px;
}

/* page visible */
/*.page-tabs{*/
/*padding: 0;*/
/*margin-bottom: 10px;*/
/*}*/
/*.page-tabs a.btn{*/
/*border-radius: 0;*/
/*background-color: #357fed;*/
/*color: #fff;*/
/*border-left: 1px solid #8E94A7;*/
/*border-right: 1px solid #8E94A7;*/
/*}*/
/*.page-tabs a.btn.active{*/
/*background-color: #fff;*/
/*border-left: 1px solid #8E94A7;*/
/*border-right: 1px solid #8E94A7;*/
/*}*/
/*.page-visible-btn{*/
/*margin-bottom: 0 !important;*/
/*padding-bottom: 0 !important;*/
/*}*/
/*.main-btn-menu {
    top: 100px !important;
    right: 21px;
    width: 50px;
    height: 30px;
}*/
/*.main-btn-menu .btn-menu {
    position: absolute;
    right: 35px;
    top: -1px;
}*/
.main-btn-sidebar {
    transition: all 0.3s;
    position: fixed !important;
    top: 130px !important;
    left: 15px;
    width: 50px;
    z-index: 999 !important;
    height: 30px;
}

.opened_menu .main-btn-sidebar {
    left: 310px;
}

#indicator-year {
    margin: 5px 10px 0 15px;
}

.block-btn-indicator-year {
    display: flex;
    justify-content: flex-end;
    width: 96%;
    margin-bottom: 15px;
    margin-top: -5px;
}

/** NEW STYLE */
.block-btn-indicator-year .btn-nav-wrap {
    top: -8px;
    right: -40px;
}

/** NEW STYLE END */

.block-btn-indicator-year .indicator-table-period {
    color: #4d8ff0;
    border: solid 1px #e0e3e8;
    padding: 3px 8px;
    border-radius: 3px;
    margin: 7px 5px 0;
    text-transform: uppercase;
    font-size: 11px;
    font-weight: 600;
}

.block.indicator-table {
    padding: 22px 0 0 0 !important;
    overflow-x: auto;
}

.block.indicator-table .btn-nav-wrap .dropdown-menu {
    left: auto !important;
    /*right: auto !important;
    top: -5px;*/
}

.block.indicator-table table.table {
    margin: 0 !important;
}

.block.indicator-table > div > a:nth-child(3) > div {
    margin-right: 5px;
}

.block-btn-indicator-year h4 {
    flex-grow: 1;
    /*margin-left: 22px;*/
}

.block-btn-indicator-year .btn-nav {
    transform: rotate(90deg);
    margin-top: 13px;
}

.table-input-all {
    width: 2300px;
    margin-bottom: 10px;
}

.table-input-all input {
    height: 30px;
}

.table-input-all .form-group {
    margin-bottom: 5px;
}

.dataTables_wrapper .dataTables_length select {
    background: #fff;
    color: #1d273e;
    border: solid 1px #d9dce2;
    height: 40px;
    border-radius: 4px;
    padding: 11px 17px;
    line-height: 1.5;
    font-size: 14px;
    -webkit-transition: .3s all;
    -moz-transition: .3s all;
    -ms-transition: .3s all;
    -o-transition: .3s all;
    transition: .3s all;
    padding-right: 42px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.dataTables_wrapper .dataTables_filter input {
    height: 30px;
    width: 244px;
    padding-left: 5px;
    border: solid 1px #d9dce2;
    background: #fff;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.next,
.dataTables_wrapper .dataTables_paginate .paginate_button.previous {
    padding: 5px;
}

.dataTables_wrapper .dataTables_paginate {
    margin-top: 5px;
}

.dataTables_wrapper .dataTables_paginate a.paginate_button:hover {
    color: #357fed !important;
    background-color: #f2f7ff;
}

.dataTables_wrapper .dataTables_paginate a.paginate_button, .dataTables_wrapper .dataTables_paginate span.ellipsis {
    border: none !important;
    font-family: 'Open Sans', sans-serif;
    font-size: 14px;
    color: #1d273e;
    border: 0;
    padding: 2px 0;
    text-align: center;
    min-width: 30px;
    height: 30px;
    border-radius: 4px;
    position: relative;
    float: left;
    margin-left: 2px;
    line-height: 1.8em;
    text-decoration: none;
    background-image: none !important;
}

.dataTables_wrapper .dataTables_paginate a.paginate_button.current {
    z-index: 2;
    color: #fff !important;
    cursor: default;
    background-color: #357fed;
    border-color: #357fed;
}

.gantt_task_progress {
    background-color: rgba(106, 106, 106, 0.1) !important;
    height: 90% !important;
}

.indicator-table .table td {
    /*width: 8.3%;*/
}

.indicator-table-unit-name {
    text-align: center;
    color: #a3a8b8;
    text-transform: uppercase;
    font-weight: 600;
    font-size: 10px;
}

.indicator-table-unit {
    text-align: center;
}

.indicator-table-unit.odd {
    background: #deffd9;
}

.indicator-table-title-row {
    color: #a3a8b8;
    text-transform: uppercase;
    font-weight: 600;
    font-size: 10px;
    vertical-align: middle !important;
    position: relative;
}

.btn-edit-all-indicator-table {
    position: absolute;
    bottom: 10px;
}

.wrap-table-input-all form {
    overflow-x: scroll;
    margin-bottom: 50px;
}

.indicator-table-title-row.plan::before {
    content: "\00a0 ";
    position: absolute;
    width: 4px;
    height: 100%;
    background: #a4b1c8;
    top: 0;
    left: -2px;
}

.indicator-table-title-row.fact::before {
    content: "\00a0 ";
    position: absolute;
    width: 4px;
    height: 100%;
    background: #2be56d;
    top: 0;
    left: -2px;
}

.indicator-table-title-row.difference::before {
    content: "\00a0 ";
    position: absolute;
    width: 4px;
    height: 100%;
    background: #485465;
    top: 0;
    left: -2px;
}

.table-input-all .form-control {
    padding: 6px 12px !important;
}

.grid-block {
    height: 100%;
}

.grid-stack-item {
    padding: 5px !important;
}

.grid-stack {
    min-height: 160px !important;
}

.grid-stack-item .grid-block.block {
    min-height: 80px !important;
}

.widget-block {
    display: block;
    height: 80px;
    border: #0000aa 1px solid;
    border-radius: 4px;
    margin: 10px 0;
    cursor: pointer;
}

.difference-cell {
    background-color: #f6f6f7;
    text-align: right;
}

.unavailable-cell {
    background: #e8e9ed;
}

.gantt.list-unstyled li {
    padding: 5px 5px 0px 5px;
}

.gantt.list-unstyled li label:hover {
    cursor: pointer;
    color: #357fed;
}

.tooltip-indicator-table-cell {
    width: 60%;
    position: absolute;
    height: 100%;
    z-index: 999;
    top: 0;
    left: 0;
}

.indicator-table-cell span.calculated {
    color: #8c93a6;
}

.indicator-table-cell {
    height: 5px;
    text-align: center;
    min-height: 20px;
}

.indicator-table-unit.even {
    background: #f2f7ff;
}

.indicator-table-unit.odd {
    background: #fffef2;
}

.loader,
.loader:after {
    border-radius: 50%;
    width: 10em;
    height: 10em;
}

.loader {
    margin: 60px auto;
    font-size: 10px;
    position: relative;
    text-indent: -9999em;
    border-top: 1.1em solid rgba(75, 46, 243, 0.2);
    border-right: 1.1em solid rgba(75, 46, 243, 0.2);
    border-bottom: 1.1em solid rgba(75, 46, 243, 0.2);
    border-left: 1.1em solid #4b2ef3;
    -webkit-transform: translateZ(0);
    -ms-transform: translateZ(0);
    transform: translateZ(0);
    -webkit-animation: load8 1.1s infinite linear;
    animation: load8 1.1s infinite linear;
}

@-webkit-keyframes load8 {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}

@keyframes load8 {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}

.sphere-block .sphere-button {
    position: absolute;
    bottom: 20px;
}

.sphere-block .favorite-button {
    position: absolute;
    top: 35px;
    right: 23px;
}

.page-visible-btn li {
    display: inline-block;
    list-style: none;
}

.btn.switcher-asc-desc i:before {
    font-size: 6px !important;
}

h3.ui-accordion-header.ui-accordion-header-active,
h3.ui-accordion-header:hover {
    color: #357fed;
    background: #f5f9ff !important;
    border-color: #d9dce2;
}

h3.ui-accordion-header {
    color: #357fed;
    background: #fff !important;
    border-color: #d9dce2;
    transition: background 0.3s;
}

.ui-accordion .ui-accordion-content {
    padding: 20px !important;
    border-color: #d9dce2;

}

.block .block-head {
    padding: 0 !important;
    margin-top: 10px;
}

.reform-view .grid-stack .grid-stack-item,
.sphere-view .grid-stack .grid-stack-item,
.user-view .grid-stack .grid-stack-item,
.goal-view .grid-stack .grid-stack-item {
    display: none;
}

.block-table-inner .block-table-inner-title {
    position: absolute;
    bottom: 5px;
    width: 90%;
    text-transform: uppercase;
    color: #8b92a8;
    font-size: 10px;
    margin-bottom: 10px;
}

.sphere-block .block-table-header {
    height: 70px;
}

.report-view {
    text-indent: 40px;
}

.anchor {
    position: relative;
    top: -100px;
    visibility: hidden;
}

/*** WIZARD TABS START ***/

.bd {
    padding: 1.5rem;
    margin-right: 0;
    margin-left: 0;
    border-width: .2rem;
}

.bd {
    position: relative;
    /*    padding: 1rem;
        margin: 1rem -15px 0;
            margin-right: -15px;
            margin-left: -15px;*/
    border: solid #f8f9fa;
    border-top-width: medium;
    border-right-width: medium;
    border-bottom-width: medium;
    border-left-width: medium;
    /*border-width: .2rem 0 0;*/
}

.bd-tabs .row {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    margin-right: -15px;
    margin-left: -15px;
}

.bd-tabs .row .col-3 {
    -ms-flex: 0 0 25%;
    flex: 0 0 25%;
    max-width: 25%;
}

.flex-column {
    -ms-flex-direction: column !important;
    flex-direction: column !important;
}

.bd-tabs .nav {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    padding-left: 0;
    margin-bottom: 0;
    list-style: none;
}

.nav-pills .nav-link.active, .nav-pills .show > .nav-link {
    color: #fff;
    background-color: #007bff;
}

.nav-pills .nav-link {
    border-radius: .25rem;
}

.nav-link {
    display: block;
    padding: .5rem 1rem;
}

.bd-tabs .col-9 {
    -ms-flex: 0 0 75%;
    flex: 0 0 75%;
    max-width: 75%;
}

/*.tab-content > .active {
    display: block;
}
.tab-content > .tab-pane {
    display: none;
}*/
.fade {
    transition: opacity .15s linear;
}

/*** WIZARD TABS END ***/

.panel-title a {
    display: block;
}

.user-nav-ava {
    border-radius: 50%;
    background: aliceblue;
    position: relative;
    width: 32px;
    height: 32px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 3px;
    color: #357fed;
}

.user-nav-ava, .user-nav-ava img {
    /*background: none !important;*/
}

.user-nav-carret {
    height: 36px !important;
    width: 36px !important;
}

.user-nav-carret:hover, .user-nav.open .btn-carret {
    color: #357fed !important;
    background: none !important;

}

.navbar-nav > .messages-menu > .dropdown-menu,
.navbar-nav > .tasks-menu > .dropdown-menu {
    width: 280px;
    padding: 0 0 0 0;
    margin: 0;
    top: 100%;
}

.navbar-nav {
    margin: 0;
    float: left;
}

/** NEW STYLE */
.language-items {
    margin: 0;
    padding: 0;
    width: 100%;
    min-width: 84px;
    height: 38px;
    padding: 4px 4px 4px 4px;
    border-radius: 4px;
    background-color: rgba(217, 220, 226, 0.5);
}

.language-items li {
    float: left;
    width: 38px;
    height: 30px;
    padding: 5px 13px 6px 12px;
    font-size: 10px;
    font-weight: 600;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.9;
    color: #1d273e;
}

.language-items li.active {
    border-radius: 4px;
    box-shadow: 0 2px 9px 0 rgba(0, 0, 0, 0.03);
    background-color: #ffffff;
    color: #357fed;
}

.methodology-block {
    padding: 0px 15px 15px 15px;
}

/** NEW STYLE END */

.breadcrumb {
    margin-bottom: 0 !important;
}

.btn-menu:before {
    font-family: "Icons";
    content: '\e800' !important;
}

.btn-sidebar:before {
    font-family: "Icons";
    content: '\e838' !important;
}

.main-btn-menu .btn-menu {
    transform: rotate(90deg);
}

.progress {
    margin-bottom: 0;
}

#login-form .form-group {
    position: relative;
}

#login-form .field-icon-eye, #reset-password-form .field-icon-eye {
    position: absolute;
    top: 46px;
    right: 11px;
    z-index: 2;
    cursor: pointer;
}

#register-form .field-icon-eye,
#form-profile .field-icon-eye {
    position: absolute;
    top: 18px;
    right: 11px;
    z-index: 2;
    cursor: pointer;
}

.hamburger {
    margin-top: -5px;
    margin-right: 0;
}

.global-search-menu-item {
    margin-top: -7px;
    margin-right: -5px;
    margin-left: 9px;
}

.task-measures-list {
    list-style: none;
}

.task-measures-list li {
    border-bottom: 1px solid #d9dce2;
    padding-top: 15px;
    padding-bottom: 15px;
}

.task-measures-list li:last-child {
    border-bottom: none;
}

.task-measures-list li .task-measure {
    padding-left: 15px;
    margin-bottom: 10px;
}

.task-measures-list li .task-measure li {
    padding-top: 5px;
    padding-bottom: 5px;
    border-bottom: none;
}

/*.select2-container--krajee {
    height: 50px;
    border-radius: 4px;
    background-color: #f5f6fa;
    padding: 6px 17px;
    color: #1d273e;
    line-height: 1.5;
    font-size: 14px;
    border: 0;
    border: solid 1px #f5f6fa;
}*/
.select2-container--krajee .select2-selection--single .select2-selection__arrow {
    border: none !important;
}

li.select2-search.select2-search--inline, li.select2-search.select2-search--inline .select2-search__field, .select2-container--krajee .select2-selection--multiple .select2-search--inline .select2-search__field {
    width: 100% !important;
    /*height: 50px !important;*/
    /*min-width: 16em !important;*/
}

.measure-block-row {
    width: 100%;
    float: left;
}

.measure-block-row .col-md-4:first-child {
    padding-right: 1px;
}

.import-error-list {
    list-style-type: decimal;
    padding-bottom: 10px;
    margin-bottom: 20px;
    border-bottom: 1px solid #eee;
}

.import-error-list li {
    margin-bottom: 10px;
}

.dropdown-menu-center {
    right: auto;
    left: 60%;
    box-shadow: 0 12px 23px 0 rgba(167, 169, 178, 0.55) !important;
    -webkit-transform: translate(-60%, 0);
    -o-transform: translate(-60%, 0);
    transform: translate(-60%, 0);
}

.dropdown-menu-single-language {
    left: -60%;
}

.mobile-menu {
    padding: 10px;
}

.user-nav-menu {
    box-shadow: 0 12px 23px 0 rgba(167, 169, 178, 0.55) !important;
}

/*
*
* ==========================================
* CUSTOM SCROLLBAR CLASSES
* ==========================================
*
*/
/* Custom Scrollbar using CSS */
.nicescroll {
    overflow-y: auto;
    scrollbar-color: #c4c4c4 #eee; /* thumb and track color */
    scrollbar-width: thin;
}

/* scrollbar width */
.nicescroll::-webkit-scrollbar {
    width: 5px;
}

/* scrollbar track */
.nicescroll::-webkit-scrollbar-track {
    background: #eee;
}

/* scrollbar handle */
.nicescroll::-webkit-scrollbar-thumb {
    border-radius: 1rem;
    background-color: #c4c4c4;
}

.box-block {
    padding: 15px 15px !important;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.blocks-background {
    background-color: #f2f7ff;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='1612' height='817' viewBox='0 0 1612 817'%3E%3Cdefs%3E%3ClinearGradient id='bx4owkozla' x1='50%25' x2='50%25' y1='0%25' y2='100%25'%3E%3Cstop offset='0%25' stop-color='%23C9D6E9'/%3E%3Cstop offset='100%25' stop-color='%23E3EAF6'/%3E%3C/linearGradient%3E%3C/defs%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='url(%23bx4owkozla)' transform='translate(0 -331)'%3E%3Cg%3E%3Cg transform='translate(30 -677) translate(-30 1008.217)'%3E%3Crect width='408' height='408' x='1088' y='322' opacity='.5' rx='103' transform='rotate(30 1292 526)'/%3E%3Crect width='408' height='408' x='75' y='333' opacity='.8' rx='103' transform='rotate(30 279 537)'/%3E%3Crect width='258' height='258' x='912' y='48' opacity='.2' rx='66' transform='rotate(30 1041 177)'/%3E%3Crect width='258' height='258' x='400' y='118' opacity='.5' rx='66' transform='rotate(30 529 247)'/%3E%3Crect width='202' height='202' x='742' y='497' rx='45' transform='rotate(30 843 598)'/%3E%3Crect width='172' height='172' x='1408' y='164' opacity='.2' rx='46' transform='rotate(30 1494 250)'/%3E%3Crect width='158' height='158' x='876' y='333' opacity='.3' rx='32' transform='rotate(30 955 412)'/%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    background-position: left -65px;
    background-repeat: no-repeat;
}

.box-content {
    height: 100%;
    flex: 0 1 auto;
}

.box-content-footer {
    padding-top: 10px;
    flex: 0 0 auto;
    min-height: 10px;
}

.info-block {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.info-block-content {
    padding: 10px;
}

.info-block-content table {
    margin-bottom: 0;
}

.no-padding .info-block-content {
    padding: 1px 5px;
}

.info-block-header {
    padding: 10px;
    border-bottom: 1px solid #d9dce2;
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 5px;
}

.block-header-title {
    border-bottom: solid 1px #e6edfb;
    padding: 0 20px;
    height: 55px;
    margin: 0;
    display: flex;
    align-items: center;
}

.label {
    padding: 2px 8px;
    font-size: 10px;
    text-transform: uppercase;
    font-weight: 600;
    line-height: 14px;
    border-radius: 2px;
}

.label-gray {
    background-color: #8aa9eb;
}

.label-primary {
    background-color: #357fed;
}

.label-dark {
    background-color: #2d374c;
}

.label-light-gray {
    background-color: #a4b1c8;
}

.text-gray {
    color: #8aa9eb;
}

.text-light-gray {
    color: #a4b1c8;
}

.text-primary {
    color: #357fed;
}

.text-success {
    color: #29C168;
}

.text-error,
.text-rejected {
    color: #DF2323;
}

.text-confirmed {
    color: #29C168;
}

.text-remarked {
    color: #F5A60A;
}

.text-dark {
    color: #2d374c;
}

.help-block {
    line-height: 1.1 !important;
    top: 5px !important;
    padding-bottom: 1px !important;
}

.text-small {
    font-size: 9px !important;
    margin-bottom: 0 !important;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}


.favorite_button_top {
    position: relative;
    margin: 5px 12px 0 0;
    /*    position: absolute !important;
        right: 50px;
        top: 7px;*/
}

.problem-block {
    display: flex;
}

.block-progress {
    margin-bottom: 20px;
}

.indicator-chart-wrapper {
    width: 400px;
}

@media screen and (max-width: 360px) {
    .indicator-chart-wrapper {
        width: 270px;
    }

    .indicator-chart-wrapper select[name="history"] {
        width: 120px;
    }
}

@media screen and (max-width: 768px) {
    .indicator-chart-wrapper {
        width: 300px;
    }

    .indicator-chart-wrapper select[name="history"] {
        width: 150px;
    }
}

.tooltip-inner {
    word-wrap: break-word;
}

.blockOverlay {
    opacity: 0.5 !important;
    position: fixed !important;
}

.modal_preloader .blockOverlay {
    background-color: #fff !important;
    position: absolute !important;
    border-radius: 6px;
}

#qty-requests-per-day {
    border: 1px solid rgb(217, 220, 226);
    padding: 10px;
    margin-right: 10px;
}

.maintenance-danger {
    color: #a94442;
    background-color: #f2dede;
    border-color: #ebccd1;
}

.maintenance {
    padding: 15px;
    margin-bottom: 20px;
    border: 1px solid transparent;
    border-radius: 4px;
}

.maintenance a {
    color: #a94442 !important;
    text-decoration: dotted;
}

.alert-message-no-basevalue {
    color: #a94442;
    background-color: #f2dede;
    border-color: #ebccd1;
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 4px;
}

.not-confirmed-values h4 {
    margin-top: 4px;
    margin-bottom: 25px;
}

.not-confirmed-values ul {
    list-style: none;
}

.not-confirmed-values ul li {
    margin-bottom: 15px;
}

.temp-indicator-value {
    position: absolute;
    margin-top: -4px;
    z-index: 9
}

.table-input-all > tbody > tr > td {
    position: relative;
}

.tooltip-indicator {
    width: 120px;
}

.block .box-content .dataTables_wrapper {
    width: 99%;
}

.site-error .error-block {
    text-align: center;
}

.site-error .error-block .error-title {
    font-weight: bold;
    font-size: 24px;
}

.site-error .error-block .error-buttons {
    margin-top: 20px;
}

.site-error .error-block .error-description {
    font-size: 14px;
}

.btn-fullscreen-table {
    position: fixed;
    right: 50px;
    bottom: 50px;
    background: #fff;
    padding: 10px;
    border: 1px solid #ddd;
    z-index: 999;
}

.btn-fullscreen-table span {
    padding: 5px;
    border-radius: 4px;
    background-color: #f2f7ff;
    border: 1px solid #f2f7ff;
    color: #357fed;
    transition: .3s all;
}

.btn-fullscreen-table span:hover {
    border: 1px solid #357fed;
}

.catalogue-main-block {
    border-radius: 4px;
    box-shadow: 0 7px 17px 0 rgba(204, 207, 221, 0.3);
    background-color: #ffffff;
    border: 1px solid #dbdce4;
}

.catalogue-block {
    padding: 23px 22px;
    margin-right: 0;
    margin-left: 0;
}

.catalogue-block:first-child {
    border-bottom: 1px solid #dbdce4;
}

.catalogue-entities-list {
}

.catalogue-entities-list li {
    padding: 7px;
}

.catalogue-entities-list li a.active,
.catalogue-entities-list li a:hover {
    font-weight: 600;
    text-decoration: underline;
}

.field-catalogue-search .input-group input {
    border: none;
    border-radius: 0;
    box-shadow: none;
    background: #fff;
}

.field-catalogue-search .input-group {
    border: 1px solid #dadce3;
    border-radius: 3px;
}

.field-catalogue-search .input-group .input-group-addon {
    border: none;
    background-color: #ffffff !important;
}

.catalogue-search-form-tags {

}

.catalogue-search-form-tags + .select2-container {
    border: 1px solid #dadce3;
    border-radius: 3px;
}

.catalogue-search-form-tags + .select2-container .selection .select2-selection {
    -webkit-box-shadow: none;
    box-shadow: none;
    background-color: #fff;
    border: none;
    border-radius: 3px;
    color: #555555;
    font-size: 14px;
    outline: 0;
}

.catalogue-search-form-tags + .select2-container .selection .select2-selection .select2-selection__rendered .select2-selection__choice {
    border-radius: 2px;
    background-color: #f2f7ff;
    color: #357fed;
    font-size: 10px;
    padding: 4px;
    margin-right: 5px;
    font-weight: bold;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    border: 1px solid #eee;
    position: relative;
    padding-right: 20px;
    text-transform: uppercase;
}

.catalogue-search-form-tags + .select2-container .selection .select2-selection .select2-selection__rendered .select2-selection__choice .select2-selection__choice__remove {
    position: absolute;
    right: 4px;
    top: 0;
    margin: 0;
    font-weight: normal;
    cursor: pointer;
}

.action-search-icon-btn {
    width: 30px;
    height: 30px;
    border-radius: 4px;
    background-color: #f2f7ff;
    border: none;
}

.action-search-icon-btn i {
    color: #357fed;
}

.action-clear-icon-btn {
    width: 30px;
    height: 30px;
    border-radius: 4px;
    background-color: #fff;
    border: none;
}

.action-search-icon-btn:hover {
    box-shadow: inset 0 0 0 1px #ccc;
}

.modile-global-search-block {
    display: none;
}

.modile-global-search-block .global-search-block {
    width: 100%;
    background-color: #fff;
    position: relative;
    top: -8px;
    border-bottom: 1px solid #dadce3;
}

.modile-global-search-block .global-search-block input {
    width: 97%;
    position: relative;
    height: 35px;
    margin: 5px;
    top: 0px;
}

.grid-top-summary {
    width: 100%;
    position: relative;
    height: 45px;
}

.grid-top-summary .page-size-selector {
    width: 30%;
    text-align: right;
    position: absolute;
    right: 0;
    top: -3px;
    bottom: 0;
    height: 30px;
}

.grid-top-summary .page-size-selector select {
    width: 71px;
    height: 30px;
    border-radius: 4px;
    border: solid 1px #d9dce2;
    background-color: #ffffff;
}

.pagination > li {
    display: inline-block;
    vertical-align: top;
}

.pagination {
    margin: 0;
    position: relative;
    text-align: center;
    width: 100%;
    padding: 0 40px;
}

.pagination .last,
.pagination .first,
.pagination .prev,
.pagination .next {
    position: absolute;
    top: 0;
}

.pagination .prev {
    left: 30px;
}
.pagination .first {
    left: 0;
}

.pagination .last {
    right: 0;
}

.pagination .next {
    right: 30px;
}

.pagination > li > a, .pagination > li > span {
    position: relative;
    float: left;
    padding: 0 3px;
    margin-left: 0;
    line-height: 34px;
    color: #1e2a36;
    text-decoration: none;
    background-color: transparent;
    width: 36px;
    height: 36px;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    border-radius: 3px;
    border: solid 1px transparent;
}

.pagination > li.next > a,
.pagination > li.next > span,
.pagination > li.prev > a,
.pagination > li.prev > span,
.pagination > li.last > a,
.pagination > li.last > span,
.pagination > li.first > a,
.pagination > li.first > span {
    color: #2081da;
}

.pagination > li.disabled > a,
.pagination > li.disabled > span {
    color: #1e2a36;
}

.pagination > li.next > a:hover,
.pagination > li.prev > span:hover,
.pagination > li.last > a:hover,
.pagination > li.first > span:hover {
    color: #fff;
}

.pagination > .active > a, .pagination > .active > a:focus, .pagination > .active > a:hover, .pagination > .active > span, .pagination > .active > span:focus, .pagination > .active > span:hover {
    z-index: 3;
    font-weight: 600;
    color: #2081da;
    cursor: default;
    background-color: transparent;
    border: solid 1px #dae3ec;
}

.pagination > li > a:focus, .pagination > li > a:hover, .pagination > li > span:focus, .pagination > li > span:hover {
    z-index: 2;
    color: #2081da;
    background-color: transparent;
    border-color: transparent;
}

.pagination .prev a,
.pagination .prev span,
.pagination .next span,
.pagination .next a,
.pagination .first a,
.pagination .first span,
.pagination .last span,
.pagination .last a {
    border: solid 1px #dae3ec;
    font-size: 22px;
}

.pagination .prev a:hover,
.pagination .prev span:hover,
.pagination .next span:hover,
.pagination .next a:hover,
.pagination .last a:hover,
.pagination .last span:hover,
.pagination .first span:hover,
.pagination .first a:hover {
    border: solid 1px #dae3ec;
    background-color: #2081da;
    color: #fff;
}

.tag-block {
    border-radius: 2px;
    background-color: #f2f7ff;
    color: #357fed;
    font-size: 10px;
    padding: 4px;
    margin-right: 5px;
    font-weight: bold;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    border: 1px solid #eee;
    text-transform: uppercase;
    display: inline-block;
    margin-bottom: 3px;
}

.tag-block:hover {
    opacity: 0.7;
}

/*.grid-view table thead tr th a {*/
/*    color: #000000;*/
/*}*/
.undo-redo-block {
    display: inline-block;
    float: left;
    margin-right: 20px;
}

.undo-redo-btn {
    width: 47px;
    height: 47px;
    border-radius: 4px;
    background-color: #ffffff;
    color: #ffffff;
    cursor: pointer;
    border: 0;
    box-shadow: 1px 1px 4px #e2e0e0;
    border: 1px solid #eee;
}

.undo-redo-btn:first-child {
    margin-right: 12px;
}

.undo-redo-btn:hover, .undo-redo-btn[disabled] {
    opacity: 0.6;
    border: 1px solid #dadce3;
}


.btn-download-block {
    margin-top: 24px;
}

.btn-download-file {
    min-width: 150px;
    border-radius: 4px;
    background-color: #f2f7ff;
    font-size: 12px !important;
    font-weight: bold;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.9;
    color: #357fed !important;
}

.btn-download-file:hover {
    opacity: 0.7;
    box-shadow: 1px 1px 4px #e2e0e0;
}

.import-form-block {
    width: 100%;
    margin-top: 25px;
    margin-bottom: 15px;
}

.import-form-block input[type="file"] {
    display: inline-block;
}

.info-message i {
    padding-right: 5px;
    color: #d9534f;
}

.import-form {
    /*width: 50%;*/
    position: relative;
    left: 0;
    right: 0;
    margin-left: auto;
    margin-right: auto;
    text-align: center;
}

#import-file-input {
    display: none;
}

.import-block {
    text-align: center;
    margin-top: 45px;
}

.btn-import-file {
    max-width: 80%;
    width: 100%;
    border-radius: 4px;
    background-color: #f2f7ff;
    font-size: 13px !important;
    font-weight: bold;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.9;
    color: #357fed !important;
}

.page-title-h1 .edit-title {
    width: 80%;
    color: #1d273e;
    border-bottom: 1px dotted #357fed;
}

.editable-block {
    border-bottom: 1px dotted #357fed;
    cursor: pointer;
}

.page-title-h1 .edit-title.edit {
    margin-top: 7px;
}

.edit-title.edit .editable-block {
    border-bottom: none;
}

.editable-block:hover {
    opacity: 0.7;
}

#edit-field {
    width: 100%;
    resize: none;
    border: 1px solid #dadce2;
    color: #1d283d;
    font-size: 24px;
    height: 55px;
    padding-left: 5px;
}

.block-edit-title-modal{
    margin-bottom: -8px;
}
.block-edit-title textarea,
.block-edit-title-modal textarea {
    width: 100%;
    display: block;
    resize: none;
    height: 80px;
    padding: 7px;
    border: 1px solid #ccc;
}

.block-edit-title-modal label{
    display: block;
    margin-bottom: 8px;
    line-height: 18px;
    font-size: 14px;
    font-weight: 400;
    color: #667080;
    text-transform: none;
}

.tree-table-history {
    width: 450px;
    float: right;
}

.tree-table-history .form-group div .undo-redo-block {
    display: block;
    float: none;
    margin-right: 0;
}

.error-block-edit-title {
    border: 1px solid #d9544e !important;
}

#edit-title-modal + .help-block {
    display: none;
}

#edit-title-modal.error-block-edit-title + .help-block {
    display: block;
    color: #d9544e;
}

#indicator-kind label,
#indicator-kind input,
#indicatorpreferencesvalue-id_entity_preferences_option label,
#indicatorpreferencesvalue-id_entity_preferences_option input {
    margin-right: 10px;
    text-decoration: underline;
    color: #1d273e;
}

#Indicator .form-control,
form textarea.form-control,
form select.form-control,
select.dropdown-button {
    background-color: #fff;
    border: solid 1px #d9dce2;
}

#Indicator .select2-container--krajee .select2-selection--single {
    height: 50px;
    line-height: 36px;
}

#Indicator .select2-container--krajee .select2-selection--single .select2-selection__arrow {
    top: 9px;
}

#Indicator .submit-popup-form {
    width: 100%;
    height: 30px;
    background-color: #357fed;
    border: none;
    margin-top: 10px;
}

#Indicator select,
form select.form-control,
select.dropdown-button {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='30' height='30' viewBox='0 0 30 30'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cpath fill='%23F2F7FF' d='M0 4c0-2.21 1.787-4 4-4h22c2.21 0 4 1.787 4 4v22c0 2.21-1.787 4-4 4H4c-2.21 0-4-1.787-4-4V4z'/%3E%3Cpath fill='%23357FED' d='M19.4 14.164l-.003-.002c.131-.142.212-.33.212-.537 0-.44-.357-.797-.796-.797-.233 0-.44.101-.586.26l-3.336 3.545-3.335-3.547-.002.002c-.145-.159-.353-.26-.585-.26-.44 0-.797.357-.797.797 0 .208.081.395.211.537l-.002.002 3.922 4.187c.151.164.364.258.588.258.223 0 .436-.094.587-.258l3.922-4.187z'/%3E%3C/g%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: top 5px right 5px;
}

#Indicator .select2-selection__arrow b {
    display: none;
}

#Indicator .select2-selection__arrow {
    margin-right: 5px;
    width: 30px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='30' height='30' viewBox='0 0 30 30'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cpath fill='%23F2F7FF' d='M0 4c0-2.21 1.787-4 4-4h22c2.21 0 4 1.787 4 4v22c0 2.21-1.787 4-4 4H4c-2.21 0-4-1.787-4-4V4z'/%3E%3Cpath fill='%23357FED' d='M19.4 14.164l-.003-.002c.131-.142.212-.33.212-.537 0-.44-.357-.797-.796-.797-.233 0-.44.101-.586.26l-3.336 3.545-3.335-3.547-.002.002c-.145-.159-.353-.26-.585-.26-.44 0-.797.357-.797.797 0 .208.081.395.211.537l-.002.002 3.922 4.187c.151.164.364.258.588.258.223 0 .436-.094.587-.258l3.922-4.187z'/%3E%3C/g%3E%3C/svg%3E");
    top: 10px;
    border-left: none;
    background-repeat: no-repeat;
}

#Indicator .form-group .select2-container .select2-selection {
    min-height: 50px !important;
    padding: 10px 6px;
    border-color: #d9dce2;
}


.tree-tooltip {
    position: absolute;
    right: 0;
    bottom: 37px;
    z-index: 99;
    text-align: center;
    line-height: 13px;
    height: 20px;
    background-color: rgba(255, 255, 255, 0.6);
}

.tree-tooltip + .tooltip {
    top: -60px !important;
    left: -20px !important;
}

.tree-tooltip + .tooltip .tooltip-inner {
    max-width: 450px;
    width: 450px;
}

.cell-number {
    font-weight: bolder;
}

.font-line-through {
    text-decoration: line-through;
}

.font-weight-normal {
    font-weight: normal;
}

/*.main-menu li{*/
/*    margin-right: 20px !important;*/
/*}*/

#constructor-id_sphere + .select2-container {
    border-radius: 3px;
}

#constructor-id_sphere + .select2-container .selection .select2-selection--single {
    height: 50px;
    line-height: 1.428571429;
    padding: 15px 24px 6px 12px;
}

#constructor-id_sphere + .select2-container .selection .select2-selection--single .select2-selection__arrow {
    top: 10px;
    right: 10px;
}

.ultimate_goal.intent .button-create {
    width: 100% !important;
}

.block-intent-nav-1 {
    display: none;
}

.zoom-1 .block-intent-nav-1 .btn-zoom-1 {
    float: left;
}

.ultimate_goal .block-intent-nav-1.block-intent-nav > div {
    width: 90%;
}

.block-intent-nav-1.block-intent-nav {
    height: 34px;
}

.zoom-1 .block-intent-nav-1,
.zoom-1 .block-intent-nav-1 button {
    display: block;
}

/* PMF */

.pmf-filter-block {
    height: 50px;
    width: 100%;
    display: flex;
    justify-content: flex-end;
    margin-bottom: 4px;
    margin-top: 5px;
}

.pmf-select-period ul {
    width: 157px;
    position: relative;
    z-index: 9;
    background-color: #fff;
    border-radius: 4px;
    -webkit-box-shadow: 0px 5px 15px -3px rgba(0, 0, 0, 0.2);
    -moz-box-shadow: 0px 5px 15px -3px rgba(0, 0, 0, 0.2);
    box-shadow: 0px 5px 15px -3px rgba(0, 0, 0, 0.2);
}

.reset-filters-btn {
    width: 157px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 15px;
    height: 38px;
    background-color: #fff;
    border-radius: 4px;
    -webkit-box-shadow: 0px 5px 15px -3px rgba(0, 0, 0, 0.2);
    -moz-box-shadow: 0px 5px 15px -3px rgba(0, 0, 0, 0.2);
    box-shadow: 0px 5px 15px -3px rgba(0, 0, 0, 0.2);
    font-size: 12px;
    color: #1D273E !important;
}

.pmf-select-period ul:after {
    content: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='30' height='30' viewBox='0 0 30 30'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cpath fill='%23F2F7FF' d='M0 4c0-2.21 1.787-4 4-4h22c2.21 0 4 1.787 4 4v22c0 2.21-1.787 4-4 4H4c-2.21 0-4-1.787-4-4V4z'/%3E%3Cpath fill='%23357FED' d='M19.4 14.164l-.003-.002c.131-.142.212-.33.212-.537 0-.44-.357-.797-.796-.797-.233 0-.44.101-.586.26l-3.336 3.545-3.335-3.547-.002.002c-.145-.159-.353-.26-.585-.26-.44 0-.797.357-.797.797 0 .208.081.395.211.537l-.002.002 3.922 4.187c.151.164.364.258.588.258.223 0 .436-.094.587-.258l3.922-4.187z'/%3E%3C/g%3E%3C/svg%3E");
    position: absolute;
    right: 4px;
    top: 4px;
    z-index: -1;
}

.pmf-select-period ul li {
    z-index: 1;
    padding: 10px 15px;
    display: none;
    list-style: none;
    font-size: 12px;
}

.pmf-select-period ul li.selected {
    display: block;
}

.pmf-select-period ul li a {
    display: block;
    color: #1D273E;
}

.pmf-table {
    background-color: #fff;
}

.pmf-table thead tr:first-child {
    height: 60px;
}

.pmf-table thead tr:first-child th {
    vertical-align: inherit;
}

.pmf-table thead tr {
    height: 62px;
}

.pmf-table thead tr th,
.pmf-table thead tr th a {
    border-bottom: 0;
    color: #A4B1C8 !important;
    font-weight: bold;
    font-size: 11px;
    text-transform: uppercase;
    font-family: 'Roboto', sans-serif;
    vertical-align: middle !important;
    background: #fff !important;
}

.pmf-table thead tr th a:not(.arrows),
.pmf-table thead tr th .align-title {
    position: relative;
    top: -30px;
}

.pmf-table,
.pmf-table tr th,
.pmf-table tr td {
    border-color: #d9dce2 !important;
}

.pmf-table td {
    background: #fff !important;
    padding: 10px !important;
    height: 80px;
}

.pmf-table .cell-indicator-name a {
    border-right: 0;
    border-left: 0;
    overflow: hidden;
    line-height: 19px;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 4;
}

.pmf-table .intermediate-values-block {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.left-arrow,
.right-arrow {
    height: 50px;
    width: 30px;
    background: #f2f7ff;
    border-radius: 3px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.pmf-table .intermediate-values-block .left-arrow:before {
    content: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='6' height='10' viewBox='0 0 6 10'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23357FED'%3E%3Cg%3E%3Cg%3E%3Cpath d='M13.336 20.21l-.002.002c-.142-.13-.33-.212-.537-.212-.44 0-.797.357-.797.797 0 .*************.585l3.545 3.337-3.547 3.335.002.002c-.16.145-.26.352-.26.585 0 .44.357.797.797.797.208 0 .395-.082.537-.212l.002.002 4.187-3.922c.164-.151.258-.364.258-.587 0-.224-.094-.436-.258-.588l-4.187-3.921z' transform='translate(-651 -337) translate(30 311) translate(609 6) matrix(-1 0 0 1 29.781 0)'/%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}

.pmf-table .intermediate-values-block .right-arrow:before {
    content: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='6' height='10' viewBox='0 0 6 10'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23357FED'%3E%3Cg%3E%3Cg%3E%3Cpath d='M13.336 20.21l-.002.002c-.142-.13-.33-.212-.537-.212-.44 0-.797.357-.797.797 0 .*************.585l3.545 3.337-3.547 3.335.002.002c-.16.145-.26.352-.26.585 0 .44.357.797.797.797.208 0 .395-.082.537-.212l.002.002 4.187-3.922c.164-.151.258-.364.258-.587 0-.224-.094-.436-.258-.588l-4.187-3.921z' transform='translate(-891 -337) translate(30 311) matrix(-1 0 0 1 879 6) matrix(-1 0 0 1 29.781 0)'/%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}

}

.table .current-values,
.pmf-table .cell-values {
    background: #f2f7ff !important;
}

.pmf-table .cell-values-text {
    color: #A4B1C8;
    font-weight: 100;
}

.pmf-table .cell-values .plan {
    color: #357fed;
    font-weight: bold;
    position: relative;
}

.pmf-table .baseline {
    color: #1ece5c;
    font-weight: bold;
    position: relative;
}

.pmf-table .end-plan {
    color: #357fed;
    font-weight: bold;
    position: relative;
}

.pmf-table .end-fact {
    color: #1ece5c;
    font-weight: bold;
    position: relative;
    top: 22px;
}

.pmf-table .fact {
    color: #1ece5c;
    font-weight: bold;
    position: relative;
    top: 22px;
}

.pmf-table .cell-progress {
    padding: 0;
    position: relative;
}

.pmf-table .progress-number {
    color: #2e384d;
    font-weight: 600;
}

.pmf-table .progress {
    height: 6px;
    border-radius: 0;
    position: absolute;
    bottom: 0;
    width: 100%;
    left: 0;
}

.pmf-table .expired-days {
    border-radius: 3px;
    position: relative;
}

.pmf-table {
    font-family: 'Roboto', sans-serif;
}

.pmf-table .cell-data-confirm .eye-block,
.pmf-table .cell-owners .eye-block {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 30px;
    height: 30px;
    background: #f0f5fe;
    border-radius: 3px;
    margin: auto;
}

.modal-box-responsible {
    display: flex;
    align-items: center;
}

.modal-box-responsible .avatar-block {
    display: block;
    width: 30px;
    height: 30px;
    margin-right: 20px
}

.modal-box-responsible .avatar-block img {
    border-radius: 3px;
}

.modal-box-responsible-hr:last-child {
    display: none;
}

.indicator-data-confirm-table thead tr th,
.indicator-data-confirm-table tfoot tr td:first-child {
    border-bottom: 0;
    color: #A4B1C8 !important;
    font-weight: bold;
    font-size: 11px;
    text-transform: uppercase;
    font-family: 'Roboto', sans-serif;
    vertical-align: middle !important;
    text-align: center;
}

.indicator-data-confirm-table {
    font-family: 'Roboto', sans-serif;
}

.indicator-data-confirm-table,
.indicator-data-confirm-table tr th,
.indicator-data-confirm-table tr td {
    border-color: #d9dce2 !important;
}

.indicator-data-confirm-table tr th:nth-child(2n),
.indicator-data-confirm-table td {
    height: 78px;
}

.indicator-data-confirm-table .header-indicator-values {
    position: relative;
    left: 100px;
}

.indicator-data-confirm-table thead tr:first-child th {
    height: 50px;
    background: #f5f6fa;
}

.indicator-data-confirm-table thead th:first-child,
.indicator-data-confirm-table tfoot td {
    text-align: center;
}

.indicator-data-confirm-table .block-inter-values {
    height: 100%;
    position: relative;
}

.indicator-data-confirm-table .block-inter-values .text {
    text-align: center;
    text-transform: uppercase;
    color: #A4B1C8;
    font-weight: bold;
    font-size: 11px;
    display: flex;
    align-items: center;
    height: 100%;
    user-select: none;
    position: absolute;
    padding: 2px;
}

.indicator-data-confirm-table .block-inter-values .arrow_up,
.indicator-data-confirm-table .block-inter-values .arrow_down {
    cursor: pointer;
    background: #f2f7ff;
    height: 30px;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 3px;
}

.indicator-data-confirm-table .block-inter-values .arrow_up {
    position: absolute;
    top: 5px;
    width: 88%;
    z-index: 10;
}

.indicator-data-confirm-table .block-inter-values .arrow_down {
    position: absolute;
    bottom: 5px;
    width: 89%;
    z-index: 10;
}

.indicator-data-confirm-table .block-inter-values .arrow_up.disable,
.indicator-data-confirm-table .block-inter-values .arrow_down.disable {
    cursor: unset;
}

.indicator-data-confirm-table .block-inter-values .arrow_up:before {
    content: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='10' height='6' viewBox='0 0 10 6'%3E%3Cpath fill='%23357FED' fill-rule='evenodd' d='M9.778 4.614l-.003.002c.139.147.225.341.225.557 0 .456-.378.827-.844.827-.247 0-.466-.105-.62-.27L5 2.051 1.466 5.732l-.002-.002c-.154.165-.373.27-.62.27C.378 6 0 5.63 0 5.173c0-.216.086-.41.224-.557l-.002-.002L4.377.268C4.537.098 4.763 0 5 0s.462.098.623.268l4.155 4.346z'/%3E%3C/svg%3E%0A");
}

.indicator-data-confirm-table .block-inter-values .arrow_down:before {
    content: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='10' height='6' viewBox='0 0 10 6'%3E%3Cpath fill='%23357FED' fill-rule='evenodd' d='M9.778 1.386l-.003-.002c.139-.147.225-.341.225-.557C10 .371 9.622 0 9.156 0c-.247 0-.466.105-.62.27L5 3.949 1.466.268 1.464.27C1.31.105 1.091 0 .844 0 .378 0 0 .37 0 .827c0 .216.086.41.224.557l-.002.002 4.155 4.346c.16.17.386.268.623.268s.462-.098.623-.268l4.155-4.346z'/%3E%3C/svg%3E");
}

.indicator-data-confirm-table .block-inter-values .arrow_up.disable:before {
    content: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='10' height='6' viewBox='0 0 10 6'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23A4B1C8'%3E%3Cg%3E%3Cpath d='M13.336 48.21l-.002.002c-.142-.13-.33-.212-.537-.212-.44 0-.797.357-.797.797 0 .*************.585l3.545 3.337-3.547 3.335.002.002c-.16.145-.26.352-.26.585 0 .44.357.797.797.797.208 0 .395-.082.537-.212l.002.002 4.187-3.922c.164-.151.258-.364.258-.587 0-.224-.094-.436-.258-.588l-4.187-3.921z' transform='translate(-310 -45) rotate(90 167.5 200.5) matrix(-1 0 0 1 29.781 0)'/%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}

.indicator-data-confirm-table .block-inter-values .arrow_down.disable:before {
    content: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='10' height='6' viewBox='0 0 10 6'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23A4B1C8'%3E%3Cg%3E%3Cpath d='M13.336 48.21l-.002.002c-.142-.13-.33-.212-.537-.212-.44 0-.797.357-.797.797 0 .*************.585l3.545 3.337-3.547 3.335.002.002c-.16.145-.26.352-.26.585 0 .44.357.797.797.797.208 0 .395-.082.537-.212l.002.002 4.187-3.922c.164-.151.258-.364.258-.587 0-.224-.094-.436-.258-.588l-4.187-3.921z' transform='translate(-310 -83) matrix(0 -1 -1 0 368 101) matrix(-1 0 0 1 29.781 0)'/%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}

.indicator-data-confirm-table .block-inter-values .arrow_up.hover:hover,
.indicator-data-confirm-table .block-inter-values .arrow_down.hover:hover {
    background: #357fed;
}

.indicator-data-confirm-table .block-inter-values .arrow_up.hover:hover:before {
    content: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='10' height='6' viewBox='0 0 10 6'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23FFF'%3E%3Cg%3E%3Cg%3E%3Cg%3E%3Cpath d='M13.336 48.21l-.002.002c-.142-.13-.33-.212-.537-.212-.44 0-.797.357-.797.797 0 .*************.585l3.545 3.337-3.547 3.335.002.002c-.16.145-.26.352-.26.585 0 .44.357.797.797.797.208 0 .395-.082.537-.212l.002.002 4.187-3.922c.164-.151.258-.364.258-.587 0-.224-.094-.436-.258-.588l-4.187-3.921z' transform='translate(-283 -435) translate(206 116) translate(24 165) rotate(90 -15.5 126.5) matrix(-1 0 0 1 29.781 0)'/%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/svg%3E%0A");
}

.indicator-data-confirm-table .block-inter-values .arrow_down.hover:hover:before {
    content: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='10' height='6' viewBox='0 0 10 6'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23FFF'%3E%3Cg%3E%3Cg%3E%3Cg%3E%3Cpath d='M13.336 48.21l-.002.002c-.142-.13-.33-.212-.537-.212-.44 0-.797.357-.797.797 0 .*************.585l3.545 3.337-3.547 3.335.002.002c-.16.145-.26.352-.26.585 0 .44.357.797.797.797.208 0 .395-.082.537-.212l.002.002 4.187-3.922c.164-.151.258-.364.258-.587 0-.224-.094-.436-.258-.588l-4.187-3.921z' transform='translate(-283 -435) translate(206 116) translate(24 165) rotate(90 -15.5 126.5) matrix(-1 0 0 1 29.781 0)'/%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/svg%3E%0A");
    transform: rotate(180deg);
    margin-top: 5px;
}

.indicator-data-confirm-table tfoot .kv-table-footer {
    border-top: 0;
}

.indicator-data-confirm-table tbody .hide {
    display: none;
}

.indicator-data-confirm-table .base-value-block,
.indicator-data-confirm-table .last-goal-value-block {
    position: relative;
    left: 40px;
}

.indicator-data-confirm-table .base-value-block .base-value {
    font-size: 24px;
    color: #1ece5c;
}

.indicator-data-confirm-table .base-value-block .base-value-time,
.indicator-data-confirm-table .last-goal-value-block .last-goal-value-time {
    font-size: 10px;
    color: #A4B1C8;
}

.indicator-data-confirm-table .last-goal-value-block .last-goal-value {
    font-size: 24px;
    color: #357fed;
}

.indicator-data-confirm-table .data-source-cell,
.indicator-data-confirm-table .confirmation-desc-cell {
    font-size: 14px;
    color: #2d374c !important;
    text-align: left;
    vertical-align: top !important;
    text-transform: inherit;
    font-weight: normal;
}

.data-confirm-title {
    margin: 20px 3px;
    padding: 10px 10px 10px 60px;
    -webkit-box-shadow: 0px 5px 15px -3px rgba(0, 0, 0, 0.12);
    -moz-box-shadow: 0px 5px 15px -3px rgba(0, 0, 0, 0.12);
    box-shadow: 0px 5px 15px -3px rgba(0, 0, 0, 0.12);
    position: relative;
    font-weight: bold;
}

.data-confirm-title p {
    color: #A4B1C8;
    font-size: 10px;
    text-transform: uppercase;
    margin-bottom: 5px;
}

.data-confirm-title:before {
    content: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='32' height='32' viewBox='0 0 32 32'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg%3E%3Cg%3E%3Cg%3E%3Cg transform='translate(-246 -205) translate(206 116) translate(24 71) translate(16 18)'%3E%3Ccircle cx='16' cy='16' r='16' fill='%23357FED'/%3E%3Cpath fill='%23FFF' fill-rule='nonzero' d='M22.594 18c.104 0 .198-.042.281-.125.083-.083.125-.177.125-.281v-7.188c0-.104-.042-.198-.125-.281-.083-.083-.177-.125-.281-.125h-1.188c-.104 0-.198.042-.281.125-.083.083-.125.177-.125.281v7.188c0 .104.042.198.125.281.083.083.177.125.281.125h1.188zm.906 4c.146 0 .266-.047.36-.14.093-.094.14-.214.14-.36v-1c0-.146-.047-.266-.14-.36-.094-.093-.214-.14-.36-.14H10v-9.5c0-.146-.047-.266-.14-.36-.094-.093-.214-.14-.36-.14h-1c-.146 0-.266.047-.36.14-.093.094-.14.214-.14.36V21c0 .27.099.505.297.703.198.198.432.297.703.297h14.5zm-6.906-4c.104 0 .198-.042.281-.125.083-.083.125-.177.125-.281v-6.188c0-.104-.042-.198-.125-.281-.083-.083-.177-.125-.281-.125h-1.188c-.104 0-.198.042-.281.125-.083.083-.125.177-.125.281v6.188c0 .104.042.198.125.281.083.083.177.125.281.125h1.188zm3 0c.104 0 .198-.042.281-.125.083-.083.125-.177.125-.281v-4.188c0-.104-.042-.198-.125-.281-.083-.083-.177-.125-.281-.125h-1.188c-.104 0-.198.042-.281.125-.083.083-.125.177-.125.281v4.188c0 .104.042.198.125.281.083.083.177.125.281.125h1.188zm-6 0c.104 0 .198-.042.281-.125.083-.083.125-.177.125-.281v-2.188c0-.104-.042-.198-.125-.281-.083-.083-.177-.125-.281-.125h-1.188c-.104 0-.198.042-.281.125-.083.083-.125.177-.125.281v2.188c0 .104.042.198.125.281.083.083.177.125.281.125h1.188z'/%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    position: absolute;
    left: 15px;
    top: 14px;
}

.indicator-data-confirm-table .plan-value {
    font-size: 14px;
    color: #357fed;
    font-weight: bold;
    user-select: none;
}

.indicator-data-confirm-table .fact-value {
    font-size: 14px;
    color: #1ece5c;
    font-weight: bold;
    user-select: none;
}

.indicator-data-confirm-table .plan-value-time,
.indicator-data-confirm-table .fact-value-time {
    font-size: 10px;
    color: #A4B1C8;
    user-select: none;
}

.indicator-data-confirm-table tfoot tr td:nth-child(2n) {
    border: 0;
}

.indicator-data-confirm-table tfoot tr td:nth-child(3n) {
    border-left: 0;
}

.indicator-data-confirm-table tr td:nth-child(4n),
.indicator-data-confirm-table tr td:nth-child(5n) {
    font-weight: normal;
    text-align: inherit;
}

.indicator-data-confirm-table tr td:nth-child(4n) {
    word-break: break-all;
}

.indicator-data-confirm-table .cell-values-text {
    color: #A4B1C8;
    font-weight: 100;
}

/*#data-confirmation-form #source_document .file-input .add-document {*/
/*    cursor: pointer;*/
/*    position: absolute;*/
/*    bottom: 26px;*/
/*    left: 39%;*/
/*    background: #fff;*/
/*    border: solid 1px #d9dce2;*/
/*    color: #357fed;*/
/*    width: 150px;*/
/*    font-size: 10px;*/
/*}*/

/*#data-confirmation-form #source_document .file-input .add-document:before {*/
/*    content: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='10' height='10' viewBox='0 0 10 10'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23357FED'%3E%3Cg%3E%3Cg%3E%3Cg%3E%3Cg%3E%3Cpath d='M16 10c.552 0 1 .448 1 1v3h3c.552 0 1 .448 1 1s-.448 1-1 1h-3v3c0 .552-.448 1-1 1s-1-.448-1-1v-3.001L12 16c-.552 0-1-.448-1-1s.448-1 1-1l3-.001V11c0-.552.448-1 1-1z' transform='translate(-576 -708) translate(216 279) translate(24 309) translate(325 110)'/%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/svg%3E%0A");*/
/*    position: relative;*/
/*    right: 4px;*/
/*    top: 2px;*/
/*}*/

/*#data-confirmation-form #source_document .fileinput-remove {*/
/*    position: absolute;*/
/*    bottom: 35px;*/
/*    right: 29px;*/
/*    z-index: 9;*/
/*    width: 30px;*/
/*    height: 30px;*/
/*    border: 0;*/
/*}*/

/*#data-confirmation-form #source_document .fileinput-remove:before {*/
/*    content: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='30' height='30' viewBox='0 0 30 30'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg%3E%3Cg%3E%3Cg%3E%3Cg%3E%3Cg transform='translate(-982 -645) translate(216 279) translate(24 309) translate(12 41) translate(730 16)'%3E%3Crect width='30' height='30' fill='%23F2F7FF' rx='4'/%3E%3Cpath fill='%23357FED' fill-rule='nonzero' d='M18.625 20.5c.313 0 .578-.11.797-.328.219-.219.328-.485.328-.797V11.5h.375c.11 0 .2-.035.27-.105.07-.07.105-.16.105-.27v-.375c0-.11-.035-.2-.105-.27-.07-.07-.16-.105-.27-.105h-1.922l-.797-1.336c-.218-.36-.547-.539-.984-.539h-2.344c-.437 0-.765.18-.984.54l-.797 1.335h-1.922c-.11 0-.2.035-.27.105-.07.07-.105.16-.105.27v.375c0 .11.035.2.105.27.07.07.16.105.27.105h.375v7.875c0 .313.11.578.328.797.219.219.485.328.797.328h6.75zm-1.734-10.125h-3.282l.422-.68c.031-.047.07-.07.117-.07h2.204c.046 0 .085.023.117.07l.422.68zm1.734 9h-6.75V11.5h6.75v7.875zm-1.781-1.125c.187 0 .281-.094.281-.281v-5.063c0-.187-.094-.281-.281-.281h-.563c-.187 0-.281.094-.281.281v5.063c0 .187.094.281.281.281h.563zm-2.625 0c.187 0 .281-.094.281-.281v-5.063c0-.187-.094-.281-.281-.281h-.563c-.187 0-.281.094-.281.281v5.063c0 .187.094.281.281.281h.563z'/%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/svg%3E%0A");*/
/*    position: absolute;*/
/*    right: 0;*/
/*    top: 0;*/
/*}*/

/*#data-confirmation-form #source_document .fileinput-remove i:before {*/
/*    display: none;*/
/*}*/

/*#data-confirmation-form .file-preview-frame {*/
/*    float: unset;*/
/*    padding: 0;*/
/*    border: 0;*/
/*}*/

/*#data-confirmation-form .file-caption-info {*/
/*    width: 100%;*/
/*    height: 18px;*/
/*    font-size: 14px;*/
/*    color: #1d273e;*/
/*}*/

/*#data-confirmation-form .file-size-info {*/
/*    width: 130px;*/
/*}*/

/*#data-confirmation-form .file-thumbnail-footer {*/
/*    height: 62px;*/
/*    display: flex;*/
/*    justify-content: space-between;*/
/*    align-items: center;*/
/*    padding: 15px;*/
/*    background: #fff;*/
/*    z-index: 9;*/
/*    border-radius: 5px;*/
/*}*/

/*#data-confirmation-form .file-footer-caption {*/
/*    text-align: start !important;*/
/*    margin-bottom: 0;*/
/*    display: flex;*/
/*    width: 100%;*/
/*}*/

/*#data-confirmation-form #source_document .file-preview {*/
/*    border: 0;*/
/*    padding: 0;*/
/*}*/

/*#data-confirmation-form #source_document .file-drop-zone {*/
/*    min-height: 90px;*/
/*    max-height: 156px;*/
/*    margin: auto;*/
/*    border: 2px dashed #d9dce2;*/
/*}*/

/*#data-confirmation-form #source_document .file-drop-zone .file-drop-zone-title {*/
/*    padding: 58px 10px;*/
/*}*/

/*#data-confirmation-form #source_document .file-actions .file-footer-buttons .kv-file-zoom,*/
/*#data-confirmation-form #source_document .file-actions .file-footer-buttons .kv-file-remove,*/
/*#data-confirmation-form #source_document .file-actions .file-footer-buttons .kv-file-upload,*/
/*#data-confirmation-form #source_document .file-thumbnail-footer .file-upload-indicator,*/
/*#data-confirmation-form #source_document .file-thumbnail-footer .text-info,*/
/*#data-confirmation-form #source_document .file-thumbnail-footer .clearfix {*/
/*    display: none;*/
/*}*/

/*#data-confirmation-form #source_document .file-preview .close {*/
/*    top: 41px;*/
/*    right: 35px;*/
/*    z-index: 99;*/
/*}*/

/*#data-confirmation-form .btn-confirm-and-save {*/
/*    width: 100%;*/
/*}*/

/*#data-confirmation-form #source_document .file-error-message {*/
/*    position: relative;*/
/*    z-index: 999;*/
/*}*/

.source-document-wrap {
    display: none;
}

.source-document-block {
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.source-document-block .download {
    width: 30px;
    height: 30px;
}

.source-document-block .download:before {
    content: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='30' height='30' viewBox='0 0 30 30'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg%3E%3Cg%3E%3Cg%3E%3Cpath fill='%23F2F7FF' d='M0 4c0-2.21 1.787-4 4-4h22c2.21 0 4 1.787 4 4v22c0 2.21-1.787 4-4 4H4c-2.21 0-4-1.787-4-4V4z' transform='translate(-1113 -375) translate(810 375) translate(303)'/%3E%3Cpath fill='%23357FED' fill-rule='nonzero' d='M19.844 22c.182 0 .337-.064.465-.191.127-.128.191-.283.191-.465v-8.969h-3.719c-.182 0-.337-.064-.465-.191-.127-.128-.191-.283-.191-.465V8h-5.469c-.182 0-.337.064-.465.191-.127.128-.191.283-.191.465v12.688c0 .182.064.337.191.465.128.127.283.191.465.191h9.188zm.656-10.5v-.164c0-.182-.064-.337-.191-.465l-2.68-2.68C17.5 8.064 17.346 8 17.164 8H17v3.5h3.5zm-5.25 8.75c-.128 0-.237-.046-.328-.137l-2.625-2.625c-.146-.127-.182-.282-.11-.465.073-.182.21-.273.41-.273h1.778v-2.188c0-.127.041-.232.123-.314.082-.082.187-.123.315-.123h.874c.128 0 .233.041.315.123.082.082.123.187.123.315v2.187h1.777c.2 0 .338.091.41.273.073.183.037.338-.109.465l-2.625 2.625c-.091.091-.2.137-.328.137z' transform='translate(-1113 -375) translate(810 375) translate(303)'/%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/svg%3E%0A");
}

.source-document-block .download:hover:before {
    content: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='30' height='30' viewBox='0 0 30 30'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg%3E%3Cg%3E%3Cg%3E%3Cpath fill='%23357FED' d='M0 4c0-2.21 1.787-4 4-4h22c2.21 0 4 1.787 4 4v22c0 2.21-1.787 4-4 4H4c-2.21 0-4-1.787-4-4V4z' transform='translate(-266 -108) translate(16 108) translate(250)'/%3E%3Cpath fill='%23FFF' fill-rule='nonzero' d='M19.844 21.75c.182 0 .337-.064.465-.191.127-.128.191-.283.191-.465v-8.969h-3.719c-.182 0-.337-.064-.465-.191-.127-.128-.191-.283-.191-.465V7.75h-5.469c-.182 0-.337.064-.465.191-.127.128-.191.283-.191.465v12.688c0 .182.064.337.191.465.128.127.283.191.465.191h9.188zm.656-10.5v-.164c0-.182-.064-.337-.191-.465l-2.68-2.68c-.128-.127-.283-.191-.465-.191H17v3.5h3.5zM15.25 20c-.128 0-.237-.046-.328-.137l-2.625-2.625c-.146-.127-.182-.282-.11-.465.073-.182.21-.273.41-.273h1.778v-2.188c0-.127.041-.232.123-.314.082-.082.187-.123.315-.123h.874c.128 0 .233.041.315.123.082.082.123.187.123.315V16.5h1.777c.2 0 .338.091.41.273.073.183.037.338-.109.465l-2.625 2.625c-.091.091-.2.137-.328.137z' transform='translate(-266 -108) translate(16 108) translate(250)'/%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}

.source-document-block .unset:before {
    content: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='30' height='30' viewBox='0 0 30 30'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg%3E%3Cg%3E%3Cg%3E%3Cpath fill='%23DF5423' d='M0 4c0-2.21 1.787-4 4-4h22c2.21 0 4 1.787 4 4v22c0 2.21-1.787 4-4 4H4c-2.21 0-4-1.787-4-4V4z' transform='translate(-267 -183) translate(17 183) translate(250)'/%3E%3Cpath fill='%23FFF' fill-rule='nonzero' d='M15 21.531c1.878 0 3.477-.66 4.799-1.982 1.321-1.322 1.982-2.921 1.982-4.799 0-1.878-.66-3.477-1.982-4.799C18.477 8.63 16.878 7.97 15 7.97c-1.878 0-3.482.665-4.813 1.996-1.312 1.312-1.968 2.907-1.968 4.785 0 1.878.66 3.477 1.982 4.799C11.523 20.87 13.122 21.53 15 21.53zm4.129-3.883l-7.027-7.027c.947-.693 2.041-.989 3.28-.889 1.24.1 2.298.588 3.173 1.463.875.875 1.358 1.933 1.449 3.172.11 1.24-.182 2.334-.875 3.281zm-1.23 1.23c-.948.694-2.042.99-3.282.89-1.24-.1-2.297-.588-3.172-1.463-.875-.875-1.367-1.933-1.476-3.172-.091-1.24.21-2.334.902-3.281l7.027 7.027z' transform='translate(-267 -183) translate(17 183) translate(250)'/%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/svg%3E%0A");
}

.source-document-block .title {
    text-decoration: underline;
    cursor: pointer;
}

.source-document-block .title:hover {
    text-decoration: none;
    color: #357fed;
}

.source-document-block .title.red {
    text-decoration: none;
    cursor: pointer;
    color: #df5423;
}

.source-document-block .title.red:hover {
    text-decoration: underline;
}

#data-confirmation-form textarea,
#data-confirmation-form input {
    background: #fff;
    /*border: solid 1px #d9dce2;*/
}

#data-confirmation-form textarea {
    height: 90px;
}

.table_goal_reform_link i {
    font-size: 13px;
    margin-right: 5px;
    transform: rotate(90deg);
    margin-top: -2px;
}

#action-action_progress {
    min-width: 60px;
}

.donors-list {
    width: 100%;
    position: absolute;
    bottom: 0;
}

.donors-list span {
    display: block;
    position: relative;
    width: 46px;
    height: 20px;
    float: left;
    margin: 1px;
    padding: 0px;
    border: 1px solid #eee;
    opacity: 0.5;
    background-color: #fff;
}

.donors-list span:hover {
    opacity: 1;
    cursor: pointer;
}

.donors-list span img {
    position: absolute;
    display: block;
    margin-top: auto;
    margin-bottom: auto;
    margin-left: auto;
    margin-right: auto;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    max-width: 100%;
    max-height: 100%;
    padding: 3px;
}

.buttons-dropdown {
    display: inline-block;
}

.buttons-dropdown .dropdown-menu {
    min-width: 180px;
}

.commitment-item .dropdown-menu {
    left: -38%;
}

.no-resize-textarea {
    resize: none;
}

.hidden-tab {
    display: none !important;
}

.parameter-table .commitment-item .dropdown-menu {
    left: -100%;
}

/*


.parameter-table {
    background: #fff;
}

.parameter-table .title {
    padding: 20px;
}

.parameter-table .subtitle {
    padding: 15px 10px;
    font-size: 9px;
    text-transform: uppercase;
    background: #f5f6fa;
    border: 0;
}

.parameter-table .regulator span {
    background: #f0f3f7;
    padding: 5px;
    border-radius: 3px;
    text-transform: uppercase;
    font-size: 9px;
    font-weight: 600;
}

.parameter-table .subtitle:first-child {
    border-left: 1px solid #d9dce2;
}

.parameter-table.general .title:first-child {
    border-right: none;
}

.parameter-table.general .title:last-child {
    border-left: none;
}

.parameter-table .subtitle:nth-child(3) {
    width: 130px;
}

.parameter-table.general .subtitle:nth-child(4) {
    width: 220px;
}

.parameter-table.general .subtitle:nth-child(5) {
    width: 120px;
}

.parameter-table.general .workplan-subtitle {
    padding: 15px;
    font-size: 10px;
    text-align: center;
    max-width: 50px;
}

.parameter-table .text {
    padding: 10px 8px;
    border-left: 0;
    border-right: 0;
    height: 130px;
    overflow: hidden;
}

.parameter-table .text:first-child {
    border-left: 1px solid #d9dce2;
    width: 80px;
}

.parameter-table .text:nth-child(2)  {
    min-width: 250px;
    max-width: 300px;
}

.parameter-table.general .text div {
    display: flex;
}

.parameter-table.general .text div > span {
    display: block;
    min-width: 30px;
    margin-right: 10px;
    margin-top: 3px;
}

.parameter-table.general .text div span img {
    width: 30px;
    border-radius: 30px;
}

.parameter-table.general .text .position {
    background: #f0f3f7;
    padding: 5px;
    border-radius: 3px;
    text-transform: uppercase;
    font-size: 9px;
    font-weight: 600;
}

.parameter-table.legal-and-impact .implementation {
    background: #f0f3f7;
    padding: 5px;
    border-radius: 3px;
    text-transform: uppercase;
    font-size: 9px;
    font-weight: 600;
    display: inline-block;
}

.parameter-table.legal-and-impact .document-link {
    color: #1d273e;
    text-decoration: underline;
}

.resource-plan .donor-img {
    width: 50px;
    border-radius: 5px;
}

.parameter-table.resource-plan .subtitle:last-child,
.parameter-table.resource-plan .text:last-child,
.parameter-table.legal-and-impact .subtitle:last-child,
.parameter-table.legal-and-impact .text:last-child,
.parameter-table.commitment .subtitle:last-child,
.parameter-table.commitment .text:last-child,
.parameter-table.donor .subtitle:last-child,
.parameter-table.donor .text:last-child
{
    border-right: 1px solid #d9dce2;
}

.block-filter {
    padding: 15px 15px;
    display: flex;
    justify-content: flex-end;
}

.block-filter .select-table {
    border: 0;
    padding: 10px 15px;
    border-radius: 3px;
    font-size: 12px;
}

.parameter-table.resource-plan .subtitle:nth-child(4),
.parameter-table.resource-plan .subtitle:nth-child(5),
.parameter-table.resource-plan .subtitle:nth-child(6),
.parameter-table.resource-plan .subtitle:nth-child(7),
.parameter-table.resource-plan .subtitle:nth-child(9),
.parameter-table.resource-plan .subtitle:nth-child(10),
.parameter-table.resource-plan .subtitle:nth-child(11) {
    width: 120px;
}

.parameter-table.commitment .subtitle:nth-child(4) {
    width: 350px;
}

.parameter-table.commitment .subtitle:nth-child(5) {
    width: 250px;
}

.parameter-table.legal-and-impact .subtitle:nth-child(4) {
    width: 250px;
}

.parameter-table.legal-and-impact .subtitle:nth-child(5) {
    width: 120px;
}

.parameter-table.legal-and-impact .subtitle:nth-child(6) {
    width: 170px;
}

.parameter-table.donor .subtitle:nth-child(4) {
    width: 165px;
}

.parameter-table.donor .subtitle:nth-child(5) {
    width: 250px;
}

.parameter-table.donor .subtitle:nth-child(6) {
    width: 110px;
}

.parameter-table.donor .subtitle:nth-child(7) {
    width: 230px;
}

.parameter-table.donor .donor-img {
    width: 30px;
    border-radius: 3px;
}



*/

.draft-document-block {
    width: 100%;
    float: left;
    padding-left: 13px;
    padding-right: 13px;
    margin-bottom: 15px;
}

.draft-documents-list {
    border-top: 1px solid #d9dce2;
    display: block;
    padding-top: 15px;
    margin-bottom: 5px;
}

.draft-documents-list li {
    width: 100%;
    padding-bottom: 10px;
}

.draft-documents-list li a:hover {
    cursor: default;
}

.disabled-link, .disabled-link:hover {
    color: #9299aa !important;
    cursor: not-allowed;
}

.gant-tooltip-container {
    visibility: hidden;
    opacity: 0;
    position: absolute;
    background: #000000;
    /*left: -38px;*/
    /*top: -130px;*/
    padding: 5px 10px;
    font-size: 12px;
    transition: .25s all ease;
    transition-delay: .1s;
    z-index: 9999;
    color: #fff;
    border-radius: 4px;
}

/* Form multiple */
#form-multiple .form-btn-block {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    width: 100%;
    height: 100%;
}

#form-multiple .parameters-block {
    border-bottom: solid 1px #d9dce2;
    margin-bottom: 20px;
    margin-top: -5px;
    padding-bottom: 10px;
}

#form-multiple .form-btn-block .buttons,
.buttons {
    display: flex;
    justify-content: center;
    padding: 5px;
}

#form-multiple .form-btn-block button,
.buttons button,
.buttons a.btn,
#form-multiple .form-btn-block .btn-link-cancel {
    min-width: 128px;
    min-height: 40px;
    max-height: 40px;
}

.btn-success {
    background: #357fed;
    border-color: #3371d9;
    color: #fff;
    font-size: 12px;
}

#form-multiple .form-btn-block button,
.buttons .btn-success, button.submit-popup-form {

    text-transform: initial;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 4px;
    margin-right: 20px;
}

.btn-success:focus,
.btn-success:hover {
    color: #fff;
    background: #205fe4 !important;
    border-color: #3371d9;
}

.save-title-from-modal {
    display: inline !important;
}

#form-multiple .form-btn-block .btn-link-cancel,
.buttons a.btn.btn-link {
    display: flex;
    justify-content: center;
    align-items: center;
    color: #357fed;
    background-color: transparent !important;
    font-size: 12px;
    border-radius: 4px;
    margin-right: 20px;
    text-transform: none !important;
}

.btn-link:hover {
    color: #357fed !important;
}

#form-multiple .name-block,
#form-multiple .commitments-block,
#form-multiple .output-block,
#form-multiple .preconditions-block,
#form-multiple .public-resources-block,
#form-multiple .timeline-block,
#form-multiple .primary-responsible-block,
#form-multiple .secondary-responsible-block,
#form-multiple .user-management-block,
#form-multiple .donor-resources {
    margin-bottom: 30px;
}

#form-multiple input,
#form-multiple select {
    height: 44px !important;
    width: 100%;
    background: #fff;
    border-color: #d9dce2;
    box-shadow: inset 0 0 0 0;
}

#form-multiple input[disabled] {
    border: solid 1px #d9dce2;
    background-color: #eeeeee;
    box-shadow: inset 0 0 0 0;
}

#form-multiple .btn-link-cancel:hover,
.block-edit-title-modal .btn-link-cancel:hover,
#form-multiple button.submit-btn:hover {
    color: #205FE4 !important;
}

#form-multiple select,
#form-multiple .select2-selection__arrow,
.form-multiple select,
.form-multiple .select2-selection__arrow {
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    appearance: none !important;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='10' height='7' viewBox='0 0 10 7'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23357FED'%3E%3Cg%3E%3Cg%3E%3Cg%3E%3Cg%3E%3Cpath d='M3.336-.79l-.002.002C3.192-.918 3.004-1 2.797-1 2.357-1 2-.643 2-.203c0 .*************.585L5.805 3.72 2.258 7.054l.002.002C2.1 7.2 2 7.408 2 7.64c0 .44.357.797.797.797.208 0 .395-.082.537-.212l.002.002 4.187-3.922c.164-.151.258-.364.258-.587 0-.224-.094-.436-.258-.588L3.336-.79z' transform='translate(-489 -435) translate(29 188) translate(25 201) translate(204) translate(231 46) rotate(90 4.89 3.719)'/%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/svg%3E") !important;
    background-repeat: no-repeat !important;
    background-position: top 18px right 10px !important;
}

#form-multiple .select2-selection__arrow,
.form-multiple .select2-selection__arrow {
    border-left: none !important;
}

#form-multiple #task-date_start,
#form-multiple #task-date_finish,
#form-multiple #action-date_start,
#form-multiple #action-date_finish {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='14' height='14' viewBox='0 0 14 14'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23357FED'%3E%3Cg%3E%3Cg%3E%3Cg%3E%3Cpath d='M235.854 42.697c.638.001 1.276-.002 1.914.007.218.003.441.014.652.065.526.126.935.436 ***********.245.294.51.302.818.005.242.018.485.028.727.**************.01.106v5.529c-.003.042-.007.084-.007.127-.003 1.07-.006 2.14-.006 3.21 0 .126-.017.244-.06.362-.244.685-.7 1.158-1.392 1.396l-.22.076h-10.7c-.056-.024-.111-.051-.168-.073-.382-.146-.716-.36-.979-.679-.193-.234-.323-.502-.424-.786-.018-.05-.03-.105-.03-.158l-.007-2.384c0-.045-.004-.09-.007-.134v-6.7l.01-.156c.01-.19-.003-.384.03-.57.153-.84.996-1.633 1.912-1.645.736-.01 1.471-.012 2.207-.018.013 0 .027.004.033.006v1.392h-.101c-.386.006-.771.009-1.157.017-.33.007-.661.019-.992.031-.147.006-.263.066-.366.178-.128.139-.172.299-.174.477-.004.447-.006.895-.008 1.342 0 .018.005.036.007.054h11.202V45.155c-.002-.159-.013-.318-.017-.478-.005-.216-.236-.525-.501-.533-.076-.002-.152-.013-.228-.015-.626-.012-1.252-.022-1.878-.032h-.173v-1.398l.048-.002zm-4.725 4.903l-3.74.012v.405c-.005 1.323-.014 2.647-.011 3.97.001.667.024 1.333.035 2 .004.224.104.388.295.503.099.06.195.111.317.112.478.004.957.02 1.435.02 1.648.006 3.295.01 4.943.007 1.185-.003 2.37-.024 3.556-.026.321 0 .614-.334.63-.571.012-.19.022-.38.022-.57-.001-1.927-.004-3.853-.007-5.779 0-.02-.002-.042-.004-.071-3.737-.007-7.466-.024-11.212 0zM232.3 42c.**************.01.077v2.647c0 .023-.003.047-.004.073H230.9c-.001-.026-.004-.048-.004-.071V42.07c0-.024.003-.047.004-.071zm2.8 0l.002.628v2.167h-1.414v-2.712c0-.028.008-.055.012-.083h1.4z' transform='translate(-483 -1573) translate(29 188) translate(24 1343) translate(204)'/%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/svg%3E") !important;
    background-repeat: no-repeat !important;
    background-position: top 15px right 10px !important;
}

#form-multiple .select2-selection__arrow b,
.form-multiple .select2-selection__arrow b {
    display: none !important;
}

.form-group .select2-container .select2-selection {
    min-height: 44px !important;
    padding: 6px 6px 6px 17px;
    border-color: #d9dce2;
}

.select2-container .select2-icon {
    height: 10px;
    margin-right: 4px;
    align-items: center;
    display: inline-block;
}

.form-group.has-success .select2-selection {
    box-shadow: inset 0 1px 1px rgb(0 0 0 / 8%), 0 0 6px rgb(102 175 233 / 60%);
}

#form-multiple .row {
    border-bottom: solid 1px #d9dce2;
}

#form-multiple .row:last-child,
#form-multiple .row:first-child {
    border-bottom: none;
}

.donor-small-flag {
    width: 28px;
    height: 20px;
    margin-right: 10px;
}

.sub-task-table tbody tr {
    height: 65px;
    border-top: solid 1px #d9dce2;
}

.sub-task-table tbody tr:last-child {
    border-bottom: solid 1px #d9dce2;
}

.sub-task-table tbody tr td {
    vertical-align: middle !important;
}

.sub-task-table tbody tr td a {
    font-size: 14px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.5;
    color: #2d374c;
    text-decoration: underline;
    cursor: pointer;
}

/* InputMaskDecimal */
.alignLeft {
    text-align: left !important;
}

/* TOOLTIP */
.tooltip-inner {
    padding: 12px 12px 12px;
    box-shadow: 0 8px 18px 0 rgba(167, 169, 178, 0.39);
    background-color: #ffffff;
    font-size: 14px !important;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    color: #1d273e;
}

.tooltip {
    font-size: 14px !important;
    font-family: 'Source Sans Pro', sans-serif;
    /* min-width: 200px; */
    min-width: 60px;
    margin-bottom: 8px;
}

.tooltip.in {
    filter: opacity(100%);
    opacity: 1;
}

.tooltip-arrow {
    position: absolute;
    width: 0;
    height: 0;
    border-color: transparent;
    border-style: solid;
}

.tooltip.right .tooltip-arrow {
    top: 50%;
    left: -3px;
    border-width: 8px 8px 8px 0;
    border-right-color: #ffffff;
}

.tooltip.left .tooltip-arrow {
    right: -3px;
    border-width: 8px 0 8px 8px;
    border-left-color: #ffffff;
}

/* .tooltip.top{
    padding: 16px 0;
} */
.tooltip.top .tooltip-arrow {
    bottom: -3px;
    border-width: 8px 8px 0;
    margin-left: -8px;
    border-top-color: #ffffff;
}

.tooltip.bottom .tooltip-arrow {
    top: -3px;
    border-width: 0 8px 8px;
    border-bottom-color: #ffffff;
}

/* Document page */
.document-title-block {
    background: #fff;
    margin-top: 20px;
}

.document-title-block .title {
    font-size: 16px;
    margin: 15px;
    color: #1d273e;
}

.map-wrap {
    background: #fff;
    border-top: solid 1px #d9dce2;
    border-right: solid 1px #d9dce2;
    height: 490px;
    overflow: hidden;
}

.map-block {
    border-left: solid 2px #d9dce2;
    padding-left: 20px;
    margin-left: 35px;
    padding-top: 20px;
    padding-bottom: 25px;
    position: relative;
}

.map-block:last-child {
    padding-bottom: 150px;
}

.map-block.active:before {
    content: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' width='34' height='34' viewBox='0 0 34 34'%3E%3Cdefs%3E%3Cpath id='prefix__a' d='M8 11.513L12.12 14 11.027 9.313 14.667 6.16 9.873 5.753 8 1.333 6.127 5.753 1.333 6.16 4.973 9.313 3.88 14z'/%3E%3C/defs%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Ccircle cx='17' cy='17' r='16.5' fill='%23357FED' stroke='%23357FED'/%3E%3Cg transform='translate(9 9)'%3E%3Cpath d='M0 0L16 0 16 16 0 16z'/%3E%3Cuse fill='%23FFF' xlink:href='%23prefix__a'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    position: absolute;
    left: -30px;
}

.map-block.inactive:before {
    content: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' width='34' height='34' viewBox='0 0 34 34'%3E%3Cdefs%3E%3Cpath id='prefix__a' d='M8 11.513L12.12 14 11.027 9.313 14.667 6.16 9.873 5.753 8 1.333 6.127 5.753 1.333 6.16 4.973 9.313 3.88 14z'/%3E%3C/defs%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Ccircle cx='17' cy='17' r='16.5' stroke='%23357FED'/%3E%3Cg transform='translate(9 9)'%3E%3Cpath d='M0 0L16 0 16 16 0 16z'/%3E%3Cuse fill='%23357FED' xlink:href='%23prefix__a'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E%0A");
    position: absolute;
    left: -30px;
}

.map-block-single {
    border-left: solid 2px #d9dce2;
    padding-left: 20px;
    margin-left: 35px;
    padding-top: 20px;
    padding-bottom: 20px;
    position: relative;
}

.map-title-single {
    color: #1d273e;
}

.map-title {
    font-size: 16px;
    color: #1d273e;
}

.map-block .deadline {
    margin-top: 20px;
}

.map-block .delay {
    color: #df5423;
    text-transform: lowercase;
}

.content-wrap {
    background: #fff;
    border-top: solid 1px #d9dce2;
    height: 490px;
}

.content-title {
    font-size: 27px;
    color: #1d273e;
    margin: 24px;
}

.content-block {
    display: flex;
    margin: 16px 24px;
    font-family: 'Source Sans Pro', sans-serif;
}

.content-block .name {
    margin-right: 30px;
    font-size: 14px;
    font-weight: 600;
    width: 160px;
    display: table;
}

.content-block .description {
    font-size: 14px;
    font-weight: 600;
}

.content-block .description strong {
    font-size: 16px;
    font-weight: bolder;
}

.content-block .label-text {
    padding: 2px 8px 3px;
    border-radius: 2px;
    background-color: #f2f7ff;
    text-transform: uppercase;
    color: #357fed;
    font-size: 10px;
    font-weight: 600;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
}

.content-wrap .link-law {
    width: 206px;
    height: 32px;
    padding: 8px 16px 9px 12px;
    border-radius: 4px;
    background-color: #f2f7ff;
    margin-top: 15px;
    margin-left: 20px;
    font-size: 12px;
    font-weight: 600;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
}

.content-wrap .link-law:before {
    content: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='10' height='11' viewBox='0 0 10 11'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23357FED' fill-rule='nonzero'%3E%3Cg%3E%3Cg%3E%3Cg%3E%3Cpath d='M15.418 18.75c.13 0 .24-.046.332-.137l4.746-4.765.703.703c.143.143.313.176.508.097.195-.078.293-.22.293-.43v-2.5c0-.13-.046-.24-.137-.331-.09-.091-.202-.137-.332-.137h-2.5c-.208 0-.351.098-.43.293-.078.195-.045.365.098.508l.703.703-4.765 4.746c-.091.091-.137.202-.137.332s.046.24.137.332l.449.45c.091.09.202.136.332.136zm4.395 2.5c.26 0 .481-.091.664-.273.182-.183.273-.404.273-.665v-2.5c0-.09-.03-.166-.088-.224-.058-.059-.133-.088-.224-.088h-.625c-.092 0-.167.03-.225.088-.059.058-.088.133-.088.224V20h-6.25v-6.25h2.813c.09 0 .166-.03.224-.088.059-.058.088-.133.088-.225v-.624c0-.092-.03-.167-.088-.225-.058-.059-.133-.088-.224-.088h-3.125c-.26 0-.482.091-.665.273-.182.183-.273.404-.273.665v6.874c0 .26.091.482.273.665.183.182.404.273.665.273h6.874z' transform='translate(-410 -735) translate(30 287) translate(367 77) translate(1 360)'/%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    margin-right: 10px;
    vertical-align: middle;
}

.pie-progress-chart {
    width: 135px;
    height: 135px;
}

.pie-wrapper {

}

.action-progress-chart {
    position: absolute;
    top: 0;
}

.action-progress-percent {
    padding-top: 50px;
    font-size: 48px;
}

.pie-red:hover {
    background-color: rgba(223, 84, 35, 0.15);
}

.pie-green:hover {
    background-color: rgba(43, 229, 109, 0.15);
}

.pie-text {
    padding-top: 37px;
    font-weight: bold;
    width: 95%;
    position: absolute;
    font-size: 42px;
    text-align: center;

}

.pie-alarm {
    border-radius: 50%;
    height: 87px;
    width: 87px;
    top: 24px;
    left: 24px;
    color: #fff;
    padding-top: 13px;
    font-weight: bold;
    position: absolute;
    font-size: 42px;
    text-align: center;
    background-color: #e45454;
    transition: all 0.3s;
}

.pie-alarm.active, .pie-total.active {
    text-decoration: underline;
}

.pie-total:hover {
    cursor: pointer;
}

.pie-total a {
    color: #333;
}

.pie-alarm:hover {
    color: #e45454;
    background-color: #fff;
    cursor: pointer;
}

.sphere-progress-wrapper {
    background-color: #fff;
    text-align: center;
    border-radius: 50%;
    height: 135px;
    width: 135px;
    box-shadow: 0 8px 18px 0 rgba(167, 169, 178, 0.39);
}

.pie-text-clickabe {
    cursor: pointer;
}

.pie-text-clickabe:hover {
    text-decoration: underline;
    font-size: 43px;
    -webkit-transition: all 0.3s;
    -moz-transition: all 0.3s;
    -ms-transition: all 0.3s;
    -o-transition: all 0.3s;
    transition: all 0.3s;
}

.pie-circle-number {
    position: absolute;
    top: -14px;
    right: 2px;
    height: 38px;
    width: 38px;
    border-radius: 50%;
    color: #fff;
    background-color: #e45454;
    font-weight: bold;
    text-align: center;
    font-size: 20px;
    padding-top: 3px;
    box-shadow: 0 8px 18px 0 rgba(167, 169, 178, 0.39);
}

.higcharts-block {
    min-height: 200px;
}

.higcharts-block .highcharts-container {
    box-shadow: 0 8px 18px 0 rgba(167, 169, 178, 0.39);
}

.img-tooltip {
    position: absolute;
    width: fit-content;
    min-width: 60px;
    min-height: 30px;
    z-index: 9;
    top: 0;
    left: 120px;
    background: #fff;
    padding: 5px;
    border-radius: 4px;
    box-shadow: 0 8px 18px 0 rgba(167, 169, 178, 0.39);
    text-align: center;
}

.img-tooltip:after {
    content: '';
    position: absolute;
    border: 10px solid transparent;
    border-right: 10px solid #fff;
    left: -20px;
    top: 6px;
}

.chart-center .highcharts-container {
    margin: 0 auto;
}

/* ---- logos ----- */
#sidebar-menu-footer {
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 90px;
    padding-right: 30px;
    text-align: center;
    margin-left: -15px;
}

#sidebar-menu-footer .sidebar-logo, .mainpage-modal-logo {
    display: inline-block;
}

#sidebar-menu-footer .sidebar-logo-surge img {
    height: 29px;
    margin: 0 16px 0 0;
}

#sidebar-menu-footer .sidebar-logo-canada img {
    height: 30px;
    margin: 0 0 5px 0;
    padding-right: 10px;
    border-right: 1px solid #1A1919;
}

.mainpage-modal-logo-canada img {
    width: 160px;
    padding-right: 20px;
    border-right: 1px solid #1A1919;
    margin: 0 16px 0 0;
}

.mainpage-modal-logo-surge img {
    width: 153px;
    margin: 11px 20px 5px 0;
}

.mainpage-modal .scroll_block {
    background-color: #edf3fd;
}

.mainpage-modal .table_block {
    margin-top: 40%;
}

.mainpage-modal .ui-draggable-handle {
    position: absolute;
    right: 5px;
}

.mainpage-modal .modal-dialog {
    width: 476px;
    padding: 0;
}

.mainpage-priorities {
    /*padding-bottom: 50px;*/
}

.mainpage-logo-text {
    text-align: center;
    color: #1d273e;
    font-size: 16px;
    transition: all 0.3s;
    padding-bottom: 10px;
}

.mainpage-logos-wrapper {
    position: absolute;
    width: 100%;
    left: 0;
    padding-bottom: 30px;
}

.modal-logos-wrapper :hover,
.mainpage-logos-wrapper:hover {
    cursor: pointer;
}

.modal-logos-wrapper:hover .mainpage-logo img,
.mainpage-logos-wrapper:hover .mainpage-logo img,
.mainpage-logos-wrapper:hover .mainpage-logo-text,
.modal-logos-wrapper:hover .mainpage-logo img,
.modal-logos-wrapper:hover .mainpage-logo-text {
    opacity: 1;
}

.mainpage-logos {
    text-align: center;
}

.mainpage-logos .mainpage-logo {
    display: inline-block;
    transition: all 0.3s;
}

.mainpage-modal-logos .mainpage-logo {
    display: inline-block;
}

.mainpage-logos .mainpage-logo-surge img {
    margin-left: 10px;
    opacity: 0.5;
    height: 33px;
    transition: all 0.3s;
}

.modal-logos-wrapper img {
    opacity: 0.5;
    transition: all 0.3s;
}

.modal-logos-wrapper:hover img {
    opacity: 1;
}

.mainpage-logos .mainpage-logo-canada img {
    opacity: 0.5;
    height: 40px;
    transition: all 0.3s;
    margin-bottom: 4px;
    padding-right: 15px;
    border-right: 1px solid #1A1919;
}

.mainpage-logos .mainpage-modal-logo-surge img {
    height: 60px;
    margin-left: 10px;
    transition: all 0.3s;
}

.mainpage-logos .mainpage-modal-logo-canada img {
    height: 60px;
    border-right: 1px solid #1A1919;
    padding-right: 20px;
}

.suggest-icon:before {
    font-family: "Icons";
    content: '\E837' !important;
}

.suggest-icon {
    padding: 4px 7px;
    background: #357fed;
    color: #fff;
    border-radius: 25%;
    cursor: pointer;
    height: 30px;
    width: 30px;
    margin: auto;
    transition: all .3s;
    border: 1px solid transparent;
}

.suggest-icon:hover {
    color: #357fed !important;
    background: #fff;
    border: 1px solid #357fed !important;
    padding: 4px 7px;
}

#Indicator .indicator-unit .select2-selection__clear {
    right: 48px !important;
    top: 13px;
}

.text-bold {
    font-weight: bold;
}

.text-italic {
    font-style: italic;
}

.page-title {
    display: flex;
    justify-content: start;
    margin-left: 15px;
    margin-right: 15px;
}

.page-title-inner-block {
    display: flex;
    justify-content: start;
}

.page-entity-name {
    text-align: center;
    margin: auto auto;
}

.page-entity-status .title-label {
    margin: auto;
    min-width: 100px;
    text-align: center;
}

.page-entity-name label {
    margin: 8px 0 5px;
    text-align: center;
}

.main-btn-menu-wrap {
    display: flex;
    justify-content: end;
}

.page-entity-name-status {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-right: 15px;
}

.page-title-inner-block h1 {
    width: 100%
}

.edit-mode-btn {
    /*display:none;*/
}

.page-entity-status {
    font-size: 8px;
    font-weight: 600;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    color: #A4B1C8;
    text-transform: uppercase;
    text-align: center;
}

.page-entity-status .title-label {
    top: 0px;
}

/* custom radio button */
.reform_preferences .custom-radio-block {
    display: flex;
    align-items: baseline;
}

.custom-radio-block label {
    /*display: block;*/
    position: relative;
    padding-left: 30px;
    margin-bottom: 12px;
    cursor: pointer;
    /*font-size: 16px;*/
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    line-height: 2.5em;
    margin-right: 15px;
}

.custom-radio-block label input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
}

.custom-checkmark {
    position: absolute;
    top: 0;
    left: 0;
    height: 25px;
    width: 25px;
    background-color: #eee;
    border-radius: 50%;
}

.custom-radio-block label:hover input ~ .custom-checkmark {
    background-color: #ccc;
}

.custom-radio-block label input:checked ~ .custom-checkmark {
    background-color: #2196F3;
}

.custom-checkmark:after {
    content: "";
    position: absolute;
    display: none;
}

.custom-radio-block label input:checked ~ .custom-checkmark:after {
    display: block;
}

.custom-radio-block label .custom-checkmark:after {
    top: 9px;
    left: 9px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: white;
}

/* custom radio button */

.navigation-button-float {
    position: fixed;
    cursor: pointer;
    border-radius: 10px;
    height: 32px;
    width: 32px;
    left: 15px;
    top: 90px;
    z-index: 99;
    background-color: rgba(53, 127, 237, 0.52);
    opacity: 0.8;
    background-repeat: no-repeat;
    background-size: 16px;
    background-position: center;
    -webkit-transition: all 0.3s;
    -moz-transition: all 0.3s;
    -ms-transition: all 0.3s;
    -o-transition: all 0.3s;
    transition: all 0.3s;
}

.navigation-button-float.active,
.navigation-button-float:hover {
    background-color: rgba(53, 127, 237, 1);
}

.navigation-content-float {
    position: fixed;
    border-radius: 10px;
    height: 80%;
    width: 450px;
    left: 15px;
    box-shadow: 0 8px 18px 0 rgba(167, 169, 178, 0.39);
    top: 115px;
    z-index: 99;
    background-color: #fff;
    background-repeat: no-repeat;
}

.bg-cancelled, .bg-canceled {
    color: #fff !important;
    background-color: #a4b1c8 !important;
}

.bg-paused {
    background-color: #F2F7FF !important;
}

.bg-expired {
    color: #fff !important;
    background-color: #667080 !important;
}

.bg-in_progress {
    color: #fff !important;
    background-color: #8aa9eb !important;
}

.bg-confirmed {
    color: #fff !important;
    background-color: #29C168 !important;
}

.bg-delay {
    color: #fff !important;
    background-color: #ebcc82 !important;
}

.bg-running-out {
    color: #fff !important;
    background-color: #DF9423 !important;
}

.bg-not_started,
.bg-not_done {
    color: #fff !important;
    background-color: #2d374c !important;
}

.bg-draft {
    color: #fff !important;
    background-color: #a4b1c891 !important;
}

.bg-finished,
.bg-done {
    color: #fff !important;
    background-color: #357fed !important;
}

.bg-failed {
    color: #fff !important;
    background-color: #df2323 !important;
}

.bg-for_approval {
    background-color: #285bab !important;
}

.text-cancelled {
    color: #a4b1c8 !important;
}

.text-paused {
    color: #F2F7FF !important;
}

.text-in_progress {
    color: #8aa9eb !important;
}

.text-for_approval {
    color: #285bab !important;
}

.text-not_done,
.text-not_started {
    color: #2d374c !important;
}

.text-finished,
.text-done {
    color: #357fed !important;
}

.select2-container--krajee-bs3 .select2-results__option[aria-disabled=true] {
    opacity: unset !important;
}

.add-catalogue-element-link {
    display: block;
}

.add-catalogue-element-link:visited {
    color: #357fed;
}

.add-catalogue-element-link:hover {
    color: #8b98d0;
}

.preloader-ajax {
    position: absolute;
    height: 55px;
    width: 100%;
    background-color: #9d9d9f6b;
    top: 0;
    opacity: 0.7;
    display: none;
    cursor: default;
}

form > .preloader-ajax {
    position: absolute;
    height: 100%;
    width: 100%;
    background-color: #9d9d9f6b;
    top: 0;
    opacity: 0.7;
    display: flex;
    cursor: default;
    z-index: 49;
    left: 0px;
}

.preloader-ajax .loader.ajax {
    height: 55px;
    width: 55px;
    font-size: 10px;
    text-indent: -9999em;
    border-top: 5px solid #beccea;
    border-right: 5px solid #beccea;
    border-bottom: 5px solid #beccea;
    border-left: 5px solid #357fed;
    -webkit-transform: translateZ(0);
    -ms-transform: translateZ(0);
    transform: translateZ(0);
    margin: auto;
}

.no-side-padding {
    padding-left: 0 !important;
    padding-right: 0 !important;
}

.align-center {
    text-align: center;
}

.level-row-name a {
    font-size: 12px;
    text-decoration: underline;
}

.bulk_approve input[type="checkbox"] {
    transform: scale(2);
    margin-right: 20px;
}

.bulk_approve--main {
    margin-bottom: 20px;
    margin-left: 20px;
    margin-right: 20px;
    float: left;
}

.approval-history-icon {
    position: absolute;
    top: 12px;
    right: 12px;
    color: #357fed;
}

.approval__checkbox--wrapper label {
    text-transform: initial !important;
    font-size: 14px;
    font-weight: 400;
    margin-left: 12px !important;
    color: #1D273E !important;

}

.field-resolutionform-agree .cbx-block {
    display: inline-block;
    margin-right: 15px;
    margin-left: 10px;
}

.selected-approval-chain-danger {
    border-top: 3px solid #e23d3d;
}

.selected-approval-chain-info {
    border-top: 3px solid #357fed;
}

.selected-approval-chain-success {
    border-top: 3px solid #3c763d;
}

.permissions-form {
    margin-right: 0px;
}

.adduser-row > div,
.adduser-row > div > label {
    margin-bottom: 0;
}

.adduser-row > div:nth-child(2) {
    margin-top: -10px;
}

.responsible-user__title {
    color: #667080;
}

.responsible-user__history > a {
    color: #357FED;
    text-decoration: underline;
}

.responsible-right-block {
    border-left: 1px solid #d9dce2;
}

.responsible-user__name {
    display: flex;
}

.responsible-user__position {
    color: #1D273E;
    font-size: 13px;
}

.responsible-user__name > i {
    margin-top: 3px;
    color: #357fed;
}

.responsible-user__name > div {
    margin-bottom: 0px;
}

.responsible-user__name > div > .help-block {
    margin-bottom: 0px;
}

.responsible-user__name label {
    color: #1D273E;
    text-transform: none;
    font-size: 14px;
    margin-bottom: 0px;
    font-weight: bold;
}

.btn-responsible-user__inherites {
    background: aliceblue;
    font-size: 14px;
    color: #1D273E;
}

.responsible-user__form_column_header {
    font-weight: bold;
    text-align: left;
}

.permissions-table {
    margin-top: 20px;
    padding-bottom: 15px;
}

.permission-checkbox > div.form-group {
    display: flex;
    align-items: baseline;
}

.permission-checkbox .control-label {
    margin-left: 10px;
    color: #1D273E;
}

.delete-responsible {
    background: #DF2323;
    color: #fff;
    margin-right: 10px;
}

.delete-responsible:hover {
    background: #DF2323 !important;
    color: #fff;
}

.flex-between {
    display: flex;
    justify-content: space-between;
}

.table-no-border,
.table-no-border thead,
.table-no-border tbody,
.table-no-border tr,
.table-no-border th,
.table-no-border td {
    border: none !important;
}

.table-no-border th.br-r-dark {
    border-right: 1px solid #e6ecf5 !important;
}

.table-no-border td.br-r-light {
    border-right: 1px solid #e6edfb !important;
}

.table-striped-alternate > tbody > tr:nth-of-type(even),
.table-striped-alternate.reverse-stripes > tbody > tr:nth-of-type(odd) {
    background-color: #F2F7FF;
}

.table-striped-alternate > tbody > tr:nth-of-type(odd),
.table-striped-alternate.reverse-stripes > tbody > tr:nth-of-type(even) {
    background-color: #FFF;
}

.approval-empty-results {
    text-align: center;
    font-weight: bold;
}

.table-striped > tbody > tr:nth-of-type(odd) {
    background-color: #fff;
}



.table-no-border th {
    background-color: #f5f6fa;
}

.table-no-border .border-column {
    border-left: 1px solid #ddd !important;
}

.permissions-users__header {
    background-color: #E7E9EE !important;
    color: #1D273E;
}

.permissions-users__child_desc {
    margin: 0px;
}

.permissions-users__header_title .vertical-text-block {
    margin: 0 auto;
    writing-mode: vertical-rl;
    width: 20px;
    scale: -1;
    height: 100%;
    display: flex;
    align-items: center;
}

.permissions-users__header_title {
    height: 100px;
    width: 64px;
}

.permissions-users__menu_btns {
    text-align: end;
}

.permissions-users__view, .permissions-history__view {
    background-color: transparent;
}

.permissions-history__view .summary, .permissions-history__view .table, .permissions-users__view .table {
    background-color: #fff;
}

.permissions-history__view .table-block, .permissions-history__view .table-block, .permissions-users__view .table-block {
    border: none;
    box-shadow: none;
}

.permissions-history__view .pagination a {
    cursor: pointer;
}

.background-white {
    background-color: #fff;
}

.permissions-users__menu_btns_inner {
    width: max-content;
    float: right;
    margin-top: 10px;
    border: 1px solid #d9dce2;
    border-radius: 4px;
    margin-bottom: 10px;
}

.permissions-users__menu_btns_inner > .btn {
    margin: 5px;
    text-decoration: underline;
    font-size: 10px;
}

.permissions-users__menu_btns_inner > .btn.active {
    background-color: #F2F7FF;
    color: #357fed;
    text-decoration: none;
}

.permissions-my__header {
    background-color: #F5F6FA !important;
    text-transform: uppercase;
}

.truncate p {
    margin: 0;
    -webkit-line-clamp: 2;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.table_title {
    -webkit-line-clamp: 1;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.truncate-document p {
    -webkit-line-clamp: 1;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.permission-checkbox label {
    text-transform: none;
    font-weight: normal;
    font-size: 14px;
}

.modal .btn-link {
    text-transform: none;
    color: #205fe4;
}

.permissions-users__role {
    color: #667080;
    font-size: 13px;
}

.permissions-my__header th {
    color: #667080;
}


.permissions-history__view .summary {
    padding-left: 10px;
}

#eds-form .btn-warning,
#resolution-form .btn-warning {
    width: 100%;
}

#eds-form .btn-confirm,
#eds-form .btn-reject,
#resolution-form .btn-confirm,
#resolution-form .btn-reject {
    font-size: 12px;
}

.select2-results__options {
    background-color: unset !important;
    color: #1d273e !important;
}

.select2-container--krajee-bs3 li.select2-results__option .small {
    color: #8c93a6 !important;
}

.select2-container--krajee-bs3 .select2-results__option--highlighted[aria-selected] .small {
    color: #fff !important;
}

.permissions-users__view thead td {
    font-weight: 600;
    color: #667080;
}

.permissions-users__view tbody td {
    vertical-align: middle !important;
    color: #1D273E;
}

.permission-checkbox .cbx-lg, .field-resolutionform-agree .cbx-lg {
    border-color: #d9dce2 !important;
    width: 24px !important;
    height: 24px !important;
    border-radius: 4px !important;
}

.permission-checkbox label.control-label {
    color: #1D273E;
}

.cbx-lg {
    font-size: 14px !important;
}

.permissions-users__view .fa-check {
    color: #1D273E;
    font-size: 18px;
}

/* 
.file-widget-wrapper .tw-px-6 {
    padding-left: 30px !important;
    padding-right: 30px !important;
} */

.row-pure .help-block {
    display: none;
}

.has-success .input-group-addon {
    border-color: #29C168;
}

.task-details-status-wrapper {
    width: 100%;
    height: 100%;
}

.task-details-status {
    font-family: Source Sans Pro;
    font-size: 20px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
}

.sub-task-public-resources td {
    padding: 0 10px !important;
}

.current-item-highlight {
    background: #FEF7E7 !important;
}

.approvals-list.container-block {
    padding: 0px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0px 4px 16px 0px rgba(204, 207, 221, 0.30);
}

.approvals-list .title {
    padding-left: 24px;
    margin-top: 16px;
    margin-bottom: 16px;
    color: #1D273E;
}

.approvals-list th {
    background-color: #F5F6FA;
    color: #667080;
    padding: 12px;
    text-transform: uppercase;
    font-size: 10px;
    font-style: normal;
    font-weight: 600;
    line-height: 14px;
}

.approvals-list td {
    padding: 12px;
    vertical-align: middle;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 18px;
}

.approvals-list .border-column {
    border-left-color: #d9dce2 !important;
}

.approvals-list table{
    border-bottom: 1px solid #E6EDFB !important;
    margin-bottom: 0px;
}

.approvals-list .button-wrapper{
    display: flex;
    justify-content: center;
    padding-top: 32px;
    padding-bottom: 32px;
}

.approval-rejected-text,
.approval-cancelled-text {
    color: #DF2323;
}

.approval-confirmed-text {
    color: #29C168;
}

.approval-expired-text {
    color: #667080;
}

.select2-selection__rendered {
    width: 95% !important;
}

.draggable.modal {
    width: 100%;
    background: none;
    overflow: hidden;
}


.ready-approval-confirm {
    background-color: darkseagreen;
}

.ready-approval-confirm i {
    color: green;
}

.ready-approval-remark {
    background-color: orange;
}

.ready-approval-remark i {
    color: orangered;
}

.ready-approval-reject {
    background-color: indianred;
}

.ready-approval-reject i {
    color: darkred;
}

.opacity04 {
    opacity: 0.4;
}

.is-in-approval-folder .approval-sign-grid-actions {
    display: none;
}

.select2-container--disabled .select2-selection__arrow {
    background-image: none !important;
}

.disabled-checkbox {
    accent-color: #8c93a6
}

.indicator-table-cell .btn-nav-wrap {
    position: relative;
    float: right;
    width: 24px;
    height: 20px;
}

.indicator-table-cell .d-block {
    position: absolute;
    width: 80%;
    text-align: right;
    right: 16px;
}

.indicator-table-cell .d-comment {
    position: absolute;
    right: 8px;
}

.d-difference {
    margin-right: 10px;
}

.select2-container--krajee-bs3 .select2-selection--single .select2-selection__clear {
    right: 40px;
}

.select2-container--krajee-bs3 .select2-selection--single .select2-selection__arrow {
    background-color: #F2F7FF;
    border-radius: 5px;
    height: 30px !important;
    top: 7px;
    right: 5px;
    width: 30px;
}

#form-multiple select, #form-multiple .select2-selection__arrow, .form-multiple select, .form-multiple .select2-selection__arrow {
    background-position: top 13px right 10px !important;
}

.has-feedback label ~ .form-control-feedback {
    top: 32px;
    background-color: #F2F7FF;
    border-radius: 4px;
    right: 5px;
    color: #357FED;
    padding: 10px;
}
.input-with-calendar{
    cursor: pointer;
}

.nav-tabs {
    border: 1px solid #ddd;
    border-radius: 4px;
    width: fit-content;
}

.nav-tabs > li {
    margin-bottom: 0;
    padding: 3px;
    font-family: Source Sans Pro;
    font-size: 10px;
    font-style: normal;
    font-weight: 700;
    line-height: 19px; /* 190% */
    text-transform: uppercase;
}

.nav-tabs > li.active > a, .nav-tabs > li.active > a:hover, .nav-tabs > li.active > a:focus {
    border: none;
    margin-left: 2px;
    background-color: #F2F7FF;
    border-radius: 4px;
    color: #357fed;
    text-decoration: unset;
}

.nav-tabs > li > a:hover {
    border-color: transparent;
    border-radius: 4px;
}

.nav-tabs > li > a {
    color: #1D273E;
    border: none;
    text-decoration: underline;
    border-radius: 4px;
}

.nav-tabs .tab-errored a {
    box-shadow: 0 0 4px #DF2323;
}

.form-title {
    padding: 15px 15px 0 15px;
    color: #1D273E;
    font-feature-settings: 'clig' off, 'liga' off;
    font-family: Source Sans Pro;
    font-size: 17px;
    font-style: normal;
    font-weight: 700;
    line-height: 23px;
}
textarea.not-resizeable{
    resize:none;
}

.indicator-table-cell .btn-nav{
    background-color: rgba(245, 246, 250, 0.32);
}
.indicator-table-cell .btn-nav:hover{
    background-color: rgba(245, 246, 250, 0.32);
}