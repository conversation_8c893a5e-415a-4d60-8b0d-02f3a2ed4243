/*@import url('https://fonts.googleapis.com/css2?family=Source+Sans+Pro:wght@400;600&display=swap');*/

/* cyrillic-ext */
@font-face {
    font-family: 'Source Sans Pro';
    font-style: normal;
    font-weight: 400;
    font-display: swap;
    src: url('../fonts/sourcesanspro/6xK3dSBYKcSV-LCoeQqfX1RYOo3qNa7lujVj9_mf.woff2') format('woff2');
    unicode-range: U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}

/* cyrillic */
@font-face {
    font-family: 'Source Sans Pro';
    font-style: normal;
    font-weight: 400;
    font-display: swap;
    src: url('../fonts/sourcesanspro/6xK3dSBYKcSV-LCoeQqfX1RYOo3qPK7lujVj9_mf.woff2') format('woff2');
    unicode-range: U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}

/* greek-ext */
@font-face {
    font-family: 'Source Sans Pro';
    font-style: normal;
    font-weight: 400;
    font-display: swap;
    src: url('../fonts/sourcesanspro/6xK3dSBYKcSV-LCoeQqfX1RYOo3qNK7lujVj9_mf.woff2') format('woff2');
    unicode-range: U+1F00-1FFF;
}

/* greek */
@font-face {
    font-family: 'Source Sans Pro';
    font-style: normal;
    font-weight: 400;
    font-display: swap;
    src: url('../fonts/sourcesanspro/6xK3dSBYKcSV-LCoeQqfX1RYOo3qO67lujVj9_mf.woff2') format('woff2');
    unicode-range: U+0370-03FF;
}

/* vietnamese */
@font-face {
    font-family: 'Source Sans Pro';
    font-style: normal;
    font-weight: 400;
    font-display: swap;
    src: url('../fonts/sourcesanspro/6xK3dSBYKcSV-LCoeQqfX1RYOo3qN67lujVj9_mf.woff2') format('woff2');
    unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB;
}

/* latin-ext */
@font-face {
    font-family: 'Source Sans Pro';
    font-style: normal;
    font-weight: 400;
    font-display: swap;
    src: url('../fonts/sourcesanspro/6xK3dSBYKcSV-LCoeQqfX1RYOo3qNq7lujVj9_mf.woff2') format('woff2');
    unicode-range: U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

/* latin */
@font-face {
    font-family: 'Source Sans Pro';
    font-style: normal;
    font-weight: 400;
    font-display: swap;
    src: url('../fonts/sourcesanspro/6xK3dSBYKcSV-LCoeQqfX1RYOo3qOK7lujVj9w.woff2') format('woff2');
    unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

/* cyrillic-ext */
@font-face {
    font-family: 'Source Sans Pro';
    font-style: normal;
    font-weight: 600;
    font-display: swap;
    src: url('../fonts/sourcesanspro/6xKydSBYKcSV-LCoeQqfX1RYOo3i54rwmhdu3cOWxy40.woff2') format('woff2');
    unicode-range: U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}

/* cyrillic */
@font-face {
    font-family: 'Source Sans Pro';
    font-style: normal;
    font-weight: 600;
    font-display: swap;
    src: url('../fonts/sourcesanspro/6xKydSBYKcSV-LCoeQqfX1RYOo3i54rwkxdu3cOWxy40.woff2') format('woff2');
    unicode-range: U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}

/* greek-ext */
@font-face {
    font-family: 'Source Sans Pro';
    font-style: normal;
    font-weight: 600;
    font-display: swap;
    src: url('../fonts/sourcesanspro/6xKydSBYKcSV-LCoeQqfX1RYOo3i54rwmxdu3cOWxy40.woff2') format('woff2');
    unicode-range: U+1F00-1FFF;
}

/* greek */
@font-face {
    font-family: 'Source Sans Pro';
    font-style: normal;
    font-weight: 600;
    font-display: swap;
    src: url('../fonts/sourcesanspro/6xKydSBYKcSV-LCoeQqfX1RYOo3i54rwlBdu3cOWxy40.woff2') format('woff2');
    unicode-range: U+0370-03FF;
}

/* vietnamese */
@font-face {
    font-family: 'Source Sans Pro';
    font-style: normal;
    font-weight: 600;
    font-display: swap;
    src: url('../fonts/sourcesanspro/6xKydSBYKcSV-LCoeQqfX1RYOo3i54rwmBdu3cOWxy40.woff2') format('woff2');
    unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB;
}

/* latin-ext */
@font-face {
    font-family: 'Source Sans Pro';
    font-style: normal;
    font-weight: 600;
    font-display: swap;
    src: url('../fonts/sourcesanspro/6xKydSBYKcSV-LCoeQqfX1RYOo3i54rwmRdu3cOWxy40.woff2') format('woff2');
    unicode-range: U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

/* latin */
@font-face {
    font-family: 'Source Sans Pro';
    font-style: normal;
    font-weight: 600;
    font-display: swap;
    src: url('../fonts/sourcesanspro/6xKydSBYKcSV-LCoeQqfX1RYOo3i54rwlxdu3cOWxw.woff2') format('woff2');
    unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

/*@import url('https://fonts.googleapis.com/css2?family=Raleway&display=swap');*/


@font-face {
    font-family: 'Icons';
    src: url('../fonts/fontello.eot');
    src: url('../fonts/fontello.eot#iefix') format('embedded-opentype'),
    url('../fonts/fontello.woff2') format('woff2'),
    url('../fonts/fontello.woff') format('woff'),
    url('../fonts/fontello.ttf') format('truetype'),
    url('../fonts/fontello.svg#fontello') format('svg');
    font-weight: normal;
    font-style: normal;
}

* {
    margin: 0;
    padding: 0;
}

html {
    height: 100%;
}

body {
    font-family: 'Source Sans Pro', sans-serif;
    font-size: 14px;
    /*font-family: 'Raleway', sans-serif;
    font-size: 14px;
    line-height: 1.5;*/
    color: #1d273e;
    width: 100%;
    height: 100%;
    -webkit-font-smoothing: antialiased;
    background-color: #f5f6fa;
}

:focus {
    outline: none;
}

a {
    color: #357fed;
}

a:focus, a:hover, a:active /*, a:visited*/
{
    color: #8b98d0;
}

a, a:focus, a:hover, a:active, a:visited {
    outline: none;
}

.alert {
    padding: 15px;
    margin-top: 15px;
    margin-bottom: 0px;
    border: 1px solid transparent;
    border-radius: 4px;
}

.mb-20 {
    margin-bottom: 20px !important;
}

.mt-20 {
    margin-top: 20px !important;
}

.mt--20 {
    margin-top: -20px !important;
}

.ml-20 {
    margin-left: 20px !important;
}

.mr-20 {
    margin-right: 20px !important;
}

.mb-24 {
    margin-bottom: 24px !important;
}

.mt-24 {
    margin-top: 24px !important;
}

.ml-24 {
    margin-left: 24px !important;
}

.mr-24 {
    margin-right: 24px !important;
}

.mb-32 {
    margin-bottom: 32px !important;
}

.mt-32 {
    margin-top: 32px !important;
}

.ml-32 {
    margin-left: 32px !important;
}

.mr-32 {
    margin-right: 32px !important;
}

.mb-10 {
    margin-bottom: 10px !important;
}

.mt-10 {
    margin-top: 10px !important;
}

.ml-10 {
    margin-left: 10px !important;
}

.mr-10 {
    margin-right: 10px !important;
}

.mb-6 {
    margin-bottom: 6px !important;
}

.mb-5 {
    margin-bottom: 5px !important;
}

.mb-4 {
    margin-bottom: 4px !important;
}

.mb-3 {
    margin-bottom: 3px !important;
}

.mb-2 {
    margin-bottom: 2px !important;
}

.mt-5 {
    margin-top: 5px !important;
}

.mt-4 {
    margin-top: 4px !important;
}

.ml-5 {
    margin-left: 5px !important;
}

.mr-5 {
    margin-right: 5px !important;
}

.m-0 {
    margin: 0;
}

.\!m-0 {
    margin: 0 !important;
}

.p-20 {
    padding: 20px !important;
}

.p-25 {
    padding: 25px !important;
}

.p-30 {
    padding: 30px !important;
}

.pl-0 {
    padding-left: 0 !important;
}

.pl-30 {
    padding-left: 30px !important;
}

.pl-45 {
    padding-left: 45px !important;
}

.pl-60 {
    padding-left: 60px !important;
}

.pl-75 {
    padding-left: 75px !important;
}

.pl-90 {
    padding-left: 90px !important;
}

.pl-24 {
    padding-left: 24px !important;
}

.pl-12 {
    padding-left: 12px !important;
}

.pl-10 {
    padding-left: 10px !important;
}

.pr-12 {
    padding-right: 12px !important;
}

.pr-0 {
    padding-right: 0 !important;
}

.pr-30 {
    padding-right: 30px !important;
}

.pr-24 {
    padding-right: 24px !important;
}

.pt-24 {
    padding-top: 24px !important;
}

.pt-8 {
    padding-top: 8px !important;
}

.pt-12 {
    padding-top: 12px !important;
}

.pt-15 {
    padding-top: 15px !important;
}

.pb-5 {
    padding-bottom: 5px !important;
}

.p-0 {
    padding: 0 !important;
}

a {
    cursor: pointer;
}

.pointer {
    cursor: pointer;
}

h1, h2, h3, h4, h5, h6 {
    color: #333;
    font-weight: 700;
}

button:focus, input:focus, textarea:focus, a:focus {
    outline: none !important;
}

input[disabled],
select[disabled] {
    border-color: #d9dce2;
    box-shadow: inset 0 0 0 0;
}

input[type=number] {
    background-color: white;
}

input[type=text] {
    padding: 12px;
    border-radius: 4px;
    border: solid 1px #d9dce2;
    background-color: #ffffff;
}

input[type=text]:focus {
    border-color: #d9dce2;
}

input[type=decimal] {
    padding: 12px;
    border-radius: 4px;
    border: solid 1px #d9dce2;
    background-color: #ffffff;
}

input[type=decimal]:focus {
    border-color: #d9dce2;
}

input.form-control, textarea.form-control {
    -webkit-appearance: none;
}

/** NEW STYLE */
.container-block {
    background-color: #ffffff;
    padding: 15px 15px;
    float: left;
    width: 100%;
    position: relative;
    margin-bottom: 22px;
}

::-webkit-input-placeholder { /* Chrome/Opera/Safari */
    font-size: 10px;
    font-weight: 600;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.9;
    color: #8c93a6;
    text-transform: uppercase;
}

::-moz-placeholder { /* Firefox 19+ */
    font-size: 10px;
    font-weight: 600;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.9;
    color: #8c93a6;
    text-transform: uppercase;
}

:-ms-input-placeholder { /* IE 10+ */
    font-size: 10px;
    font-weight: 600;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.9;
    color: #8c93a6;
    text-transform: uppercase;
}

:-moz-placeholder { /* Firefox 18- */
    font-size: 10px;
    font-weight: 600;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.9;
    color: #8c93a6;
    text-transform: uppercase;
}

.title-label {
    margin: 7px 13px 6px 0;
    padding: 2px 4px 3px;
    border-radius: 2px;
    font-size: 10px;
    font-weight: 600;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    color: #ffffff;
    position: relative;
    top: -5px;
}

.no-padding {
    padding-right: 0 !important;
    padding-left: 0 !important;
}

table.table-no-padding td {
    padding: 0 !important;
}

.no-padding table tr td:first-child,
.no-padding table tr th:first-child {
    padding-left: 20px;
}

.no-padding table tr td:last-child,
.no-padding table tr th:last-child {
    padding-right: 20px;
}

/** NEW STYLE END */

select::-ms-expand {
    display: none;
}

ul, ol {
    list-style-position: inside;
}

table {
    width: 100%;
    margin: 0;
}

table td,
table th {
    padding: 15px 22px;
    border: solid 1px #d9dce2;
    vertical-align: top;
}

table th {
    background-color: #f5f6fa;
}

.popup_menu_btn {
    display: none;
}

.list-inline {
    margin: 0;
}

.list-inline > li {
    padding: 0;
}

[class^="icon-"]:before, [class*=" icon-"]:before {
    font-family: "Icons";
    font-style: normal;
    font-weight: normal;
    speak: none;
    display: inline-block;
    text-decoration: inherit;
    text-align: center;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
}

.icon-dot-3:before {
    content: '\e800';
}

/* 'î €' */
.icon-down-dir:before {
    content: '\e801';
}

/* 'î ' */
.icon-up-dir:before {
    content: '\e802';
}

/* 'î ‚' */
.icon-left-dir:before {
    content: '\e803';
}

/* 'î ƒ' */
.icon-right-dir:before {
    content: '\e804';
}

/* 'î „' */
.icon-th-1:before {
    content: '\e805';
}

/* 'î …' */
.icon-trash-empty:before {
    content: '\e806';
}

.icon-ok:before {
    content: '\e823';
}

/* 'î £' */
.icon-down:before {
    content: '\e82e';
}

/* 'î ®' */
.icon-up:before {
    content: '\e82f';
}

/* 'î ¯' */
.icon-nav:before {
    content: '\e830';
}

/* 'î °' */
.icon-table:before {
    content: '\e831';
}

/* 'î ±' */
.icon-tree:before {
    content: '\e832';
}

/* 'î ²' */
.icon-carret:before {
    content: '\e833';
}

/* 'î ³' */
.icon-search:before {
    content: '\e835';
}

/* 'î µ' */
.icon-pause:before {
    content: '\e836';
}

/* 'î ¶' */
.icon-plus:before {
    content: '\e837';
}

/* 'î ·' */
.icon-pen:before {
    content: '\e82c';
}

/* 'î ·' */
.icon-right-big:before {
    content: '\e838';
}

/* 'î ¸' */
.icon-add:before {
    content: '\e839';
}

/* 'î ¹' */
.icon-angle-left:before {
    content: '\f104';
}

/* 'ï„„' */
.icon-angle-right:before {
    content: '\f105';
}

/* 'ï„…' */
.icon-angle-up:before {
    content: '\f106';
}

/* 'ï„†' */
.icon-angle-down:before {
    content: '\f107';
}

/* 'ï„‡' */
.icon-menu_:before {
    content: '\e824';
}

/* 'î ¤' */
.icon-th-lg:before {
    content: '\e83a';
}

/* 'î º' */
.icon-th-md:before {
    content: '\e83b';
}

/* 'î »' */
.icon-th:before {
    content: '\e83c';
}

/* 'î ¼' */
.icon-clock:before {
    content: '\e83d';
}

/* 'î ½' */
.icon-comment-bg:before {
    content: '\e83e';
}

/* 'î ¾' */
.icon-comment:before {
    content: '\e83f';
}

/* 'î ¿' */
.icon-calendar:before {
    content: '\e840';
}

/* 'î¡€' */
.icon-doc:before {
    content: '\e841';
}

/* 'î¡' */
.icon-close:before {
    content: '\e82d';
}

/* 'î ­' */
.icon-star-empty:before {
    content: '\e842';
}

/* 'î¡‚' */
.icon-star:before {
    content: '\e843';
}

/* 'î¡ƒ' */
.icon-out:before {
    content: '\e845';
}

/* 'î¡…' */
.icon-plus-circle:before {
    content: '\e846';
}

/* 'î¡†' */
.icon-profile-image:before {
    content: '\e847';
}

/* 'î¡‡' */
.icon-setting:before {
    content: '\e848';
}

/* 'î¡ˆ' */
.icon-profile:before {
    content: '\e849';
}

/* 'î¡‰' */
.icon-alert:before {
    content: '\e84a';
}

/* 'î¡Š' */
.icon-info:before {
    content: '\e84b';
}

/* 'î¡‹' */
.icon-check:before {
    content: '\e84c';
}

/* 'î¡Œ' */
.icon-cancel:before {
    content: '\e84d';
}

/* 'î¡' */
.icon-block:before {
    content: '\e84e';
}

/* 'î¡Ž' */
.icon-list:before {
    content: '\e850';
}

/* 'Ð¾ÐŽÑ’' */
.icon-load:before {
    content: '\e851';
}

/* 'Ð¾ÐŽâ€˜' */

.pagination > li > a, .pagination > li > span {
    font-family: 'Open Sans', sans-serif;
    font-size: 14px;
    line-height: 1.43;
    color: #1d273e;
    border: 0;
    padding: 0;
    text-align: center;
    width: 30px;
    height: 30px;
    line-height: 28px;
    border-radius: 4px;
}

.pagination > li.active > a, .pagination > li > a:focus, .pagination > li > a:hover, .pagination > li > span:focus, .pagination > li > span:hover {
    color: #357fed;
    background-color: #f2f7ff;
    font-weight: 600;
}

.btn30 .icon-angle-left, .btn30 .icon-angle-right {
    font-size: 18px;
}

.kalendare-btn {
    width: 30px;
    height: 30px;
}

.kalendare-btn input {
    width: 30px;
    height: 30px;
    border: 0;
    cursor: pointer;
}

.kalendare-btn.open:before {
    background-color: #357fed !important;
    color: #fff !important;
}

.kalendare-group.kalendare-btn:before {
    right: 0;
}

.btn-view.disabled {
    opacity: 0.7;
    background-color: #f5f6fa;
}

.datepicker table tr td.disabled {
    background-color: #f5f6fa !important;
}

.datepicker.datepicker-dropdown {
    z-index: 999 !important;
}

.view-drop .dropdown-menu {
    min-width: 33px;
    border-radius: 4px;
    -webkit-box-shadow: 0 8px 18px 0 rgba(167, 169, 178, 0.39);
    -moz-box-shadow: 0 8px 18px 0 rgba(167, 169, 178, 0.39);
    box-shadow: 0 8px 18px 0 rgba(167, 169, 178, 0.39);
    background-color: #fff;
    top: auto;
    bottom: 100%;
    margin-bottom: 10px;
    padding: 0;
}

.view-drop .dropdown-menu a {
    color: #357fed;
    display: block;
    height: 30px;
    border-bottom: solid 1px #dedfe5;
    line-height: 30px;
    text-align: center;
    padding: 0;
}

.view-drop .dropdown-menu li:last-child a {
    border: 0;
}

.btn-view {
    height: 38px;
    border-radius: 4px;
    -webkit-box-shadow: 0 8px 18px 0 rgba(167, 169, 178, 0.39);
    -moz-box-shadow: 0 8px 18px 0 rgba(167, 169, 178, 0.39);
    box-shadow: 0 8px 18px 0 rgba(167, 169, 178, 0.39);
    background-color: #fff;
    display: inline-block;
    cursor: pointer;
}

.btn-view-left {
    float: left;
    font-family: 'Open Sans', sans-serif;
    font-size: 11px;
    font-weight: 600;
    line-height: 1.91;
    color: #1d273e;
    padding: 7px;
    text-align: center;
}

.btn-view-left .icon-angle-down {
    font-size: 14px;
    line-height: 1;
    display: inline-block;
    margin-left: 5px;
    position: relative;
    top: 1px;
}

.btn-view-left .icon-angle-down:before {
    font-weight: 700;
}

.btn-view-icon {
    width: 24px;
    height: 24px;
    border-radius: 4px;
    background-color: #357fed;
    display: inline-block;
    vertical-align: middle;
    text-align: center;
    line-height: 24px;
    color: #fff;
    margin-right: 5px;
}

.btn-view .icon-load {
    width: 38px;
    height: 38px;
    background: transparent;
    float: right;
    border: 0;
    border-left: solid 1px #dedfe5;
    font-size: 16px;
}

.icon-plus {
    margin-right: 8px;
    font-weight: normal;
}

.icon-plus:before {
    font-family: "Icons";
    content: '\e837';
}

/*button*/
.btn:after {
    position: absolute;
    z-index: -1;
    top: -50%;
    left: 50%;
    border-radius: 50px;
    -webkit-transition: .3s all ease-out;
    -moz-transition: .3s all ease-out;
    -ms-transition: .3s all ease-out;
    -o-transition: .3s all ease-out;
    transition: .3s all ease-out;
    height: 200%;
    width: 0;
}

.btn-primary:after {
    background-color: #205fe4;
    content: "";
}

.btn-default:after {
    /*background-color: #f5f9ff;*/
    /*content: "";*/
}

.btn:focus:after, .btn:active:after, .btn:hover:after {
    width: 106%;
    left: -3%;
}

.btn {
    border-radius: 4px;
    font-weight: 600;
    padding: 7px 12px;
    text-transform: none;
    text-decoration: none;
    position: relative;
    overflow: hidden;
    line-height: 16px;
    font-size: 12px;
    font-family: "Source Sans Pro", sans-serif;
    /*    -webkit-transform: perspective(1px) translateZ(0);
        -moz-transform: perspective(1px) translateZ(0);
        -ms-transform: perspective(1px) translateZ(0);
        -o-transform: perspective(1px) translateZ(0);
        transform: perspective(1px) translateZ(0);*/
}

.btn-circle {
    width: 267px;
    padding: 16px 25px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 14px;
    font-weight: bold;
    text-transform: inherit;
    line-height: 1.2;
    border-radius: 50px;
}

.btn-default {
    color: #357fed;
    background-color: #fff;
    border-color: #d9dce2;
}

.btn-default:visited, .btn-default:active, .btn-default:focus, .btn-default:hover {
    color: #357fed;
}

.btn-default.btn-circle {
    border-color: #357fed;
}

/*.btn-default:hover{
    color: #357fed;
    background-color: #f5f9ff;
    border-color: #357fed;
}*/
.btn-primary {
    background-color: #357fed;
    border-color: #357fed;
    color: #fff;
}

.btn-primary:visited, .btn-primary:active, .btn-primary:focus, .btn-primary:hover {
    color: #fff;
}

.btn-outline {
    background-color: #fff;
    border-color: #357fed;
    color: #357FED;
}

.btn-outline:active, .btn-outline:focus, .btn-outline:hover {
    color: #1d273e;
    /*border-color: #1d273e;*/
}

.btn-info:after {
    background-color: #357fed;
    content: "";
}

.btn-info {
    color: #357fed;
    background-color: #f2f7ff;
    border-color: #f2f7ff;
}

.label-info {
    float: left;
    margin-right: 5px;
    border-radius: 4px;
    background-color: #f2f7ff;
    font-family: 'Open Sans', sans-serif;
    font-size: 11px;
    font-weight: 600;
    line-height: 1.1;
    color: #357fed;
    padding: 6px;
    text-align: center;
    border-color: #f2f7ff;
}

.btn-info:focus, .btn-info:hover {
    color: #fff;
}

.btn-link {
    font-size: 12px;
    line-height: 16px;
    line-height: 1.5;
}

.btn-link:hover {
    color: #357fed;
    text-decoration: underline;
}

a, .btn, [class^="btn-"] {
    -webkit-transition: .3s all;
    -moz-transition: .3s all;
    -ms-transition: .3s all;
    -o-transition: .3s all;
    transition: .3s all;
}

.btn-grey {
    background-color: #f2f7ff;
    border-color: #f2f7ff;
    color: #357fed;
}

.btn-grey:hover {
    background-color: #d4e5ff;
    color: #357fed;
}

.btn30 {
    width: 30px;
    height: 30px;
    border-radius: 4px;
    display: inline-block;
    text-align: center;
    line-height: 28px;
    text-decoration: none !important;
    -webkit-transition: .3s all;
    -moz-transition: .3s all;
    -ms-transition: .3s all;
    -o-transition: .3s all;
    transition: .3s all;
    padding: 0 !important;
}

.btn17 {
    width: 17px;
    height: 17px;
    line-height: 15px;
    border-radius: 4px;
    display: inline-block;
    text-align: center;
    text-decoration: none !important;
    -webkit-transition: .3s all;
    -moz-transition: .3s all;
    -ms-transition: .3s all;
    -o-transition: .3s all;
    transition: .3s all;
    padding: 0 !important;
}

.btn30 i, .btn30 span, .btn17 i, .btn17 span {
    margin: 0 !important;
}

.btn30 .icon-trash-empty:before {
    font-size: 18px;
    line-height: 28px;
}

.btn-linkin {
    text-decoration: underline;
    text-transform: inherit;
    font-weight: normal;
    font-size: 14px;
    line-height: 1.5;
}

.btn-linkin span {
    margin-left: 10px;
}

.btn-linkin .icon-close:before {
    font-size: 9px;
}

.btn-white {
    border: solid 1px #d9dce2;
    background: transparent;
    color: #357fed;
}

.btn-white:hover {
    -webkit-box-shadow: inset 0 0 0 1px #d9dce2;
    -moz-box-shadow: inset 0 0 0 1px #d9dce2;
    box-shadow: inset 0 0 0 1px #d9dce2;
    background: transparent;
    color: #357fed;
}

.btn-grey.open,
.btn-grey.active,
    /*.open .btn-grey, */
    /*.active .btn-grey, */
.btn-white.open,
.btn-white.active
    /*.open .btn-white, */
    /*.active .btn-white */
{
    background-color: #357fed;
    color: #fff;
    border-color: #357fed;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
}

.btn-danger {
    color: #e23d3d;
    background-color: #fcebeb;
    border-color: #fcebeb;
}

.btn-danger:hover {
    color: #e23d3d;
    border-color: #e23d3d;
}

.kalendare-group {
    position: relative;
}

.auto-kal {
    font-family: 'Open Sans', sans-serif;
}

.kalendare-group:before {
    pointer-events: none;
    position: absolute;
    width: 30px;
    height: 30px;
    border-radius: 4px;
    background-color: #f2f7ff;
    top: 50%;
    margin-top: -15px;
    z-index: 10;
    right: 6px;
    color: #357fed;
    border: 0;
    -webkit-transition: .3s all ease-in-out;
    -moz-transition: .3s all ease-in-out;
    -ms-transition: .3s all ease-in-out;
    -o-transition: .3s all ease-in-out;
    transition: .3s all ease-in-out;
    font-family: 'Icons';
    content: '\e840';
    font-size: 15px;
    text-align: center;
    line-height: 30px;
    cursor: pointer;
}

.kalendare-group:hover:before {
    background-color: #d4e5ff;
}

.scroll-block {
    padding-bottom: 10px !important;
}

/*elements*/
h1, .section-title {
    font-size: 24px;
    font-weight: 600;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    color: #1d273e;
    min-height: 33px;
}

h5, .block-title {
    font-size: 17px;
    font-weight: bold;
    line-height: 1.35;
    color: #1d273e;
}

.sm-title {
    font-size: 16px;
    font-weight: 600;
    line-height: 1.19;
    color: #1d273e;
    margin: 0 0 17px;
}

.xs-title a {
    text-decoration: underline;
}

h6, .xs-title {
    font-size: 14px;
    font-weight: 600;
    line-height: 1.43;
    color: #1d273e;
    margin: 0 0 15px;
}

/*ul:not([class]) li{*/
/*margin-bottom: 7px;*/
/*position: relative;*/
/*padding-left: 14px;*/
/*list-style: none;*/
/*}*/
/*ul:not([class]) li:before{*/
/*position: absolute;*/
/*top: 7px;*/
/*left: 0;*/
/*width: 4px;*/
/*height: 4px;*/
/*background-color: #357fed;*/
/*border-radius: 50%;*/
/*z-index: 1;*/
/*content: '';*/
/*}*/

.checkbox-section, .radio-section {
    margin-top: 12px;
}

.radio_btn, .checkbox_btn {
    display: none !important;
}

.radio_btn + label, .checkbox_btn + label {
    text-transform: initial;
    text-decoration: underline;
    position: relative;
    padding-left: 33px;
    margin-bottom: 12px;
    cursor: pointer;
    color: #1d273e;
    font-weight: normal;
    font-size: 14px;
}

.lg-btn-section .radio_btn + label, .lg-btn-section .checkbox_btn + label {
    padding-left: 45px;
    margin-bottom: 25px;
    font-size: 16px;
}

.radio_btn:hover + label, .checkbox_btn:hover + label {
    color: #357fed;
}

.radio_btn:hover + label:before, .checkbox_btn:hover + label:before {
    -webkit-box-shadow: inset 0 0 0 1px #d9dce2;
    -moz-box-shadow: inset 0 0 0 1px #d9dce2;
    box-shadow: inset 0 0 0 1px #d9dce2;
}

.checkbox_btn + label:before, .radio_btn + label:before {
    width: 20px;
    height: 20px;
    object-fit: contain;
    border-radius: 4px;
    border: solid 1px #d9dce2;
    background-color: #fff;
    position: absolute;
    top: -1px;
    left: 0;
    line-height: 18px;
    text-align: center;
    content: '';
    -webkit-transition: .3s all ease-in-out;
    -moz-transition: .3s all ease-in-out;
    -ms-transition: .3s all ease-in-out;
    -o-transition: .3s all ease-in-out;
    transition: .3s all ease-in-out;
    font-size: 7px;
}

.checkbox_btn:disabled {
    background-color: #f5f6fa;
}

.checkbox_btn:disabled + label, .radio_btn:disabled + label, input:disabled + .slider {
    pointer-events: none;
}

.radio_btn:disabled {
    border: solid 2px #d9dce2;
}

.lg-btn-section .checkbox_btn + label:before, .lg-btn-section .radio_btn + label:before {
    width: 30px;
    height: 30px;
    top: -4px;
    line-height: 28px;
    font-size: 10px;
}

.lg-btn-section .radio_btn + label:after {
    width: 12px;
    height: 12px;
    left: 9px;
}

.checkbox_btn + label:before {
    border-radius: 4px;
    color: #fff;
    content: '\e823';
    font-family: 'Icons';
}

.checkbox_btn:checked + label:before, .radio_btn:checked + label:before {
    -webkit-box-shadow: inone;
    -moz-box-shadow: none;
    box-shadow: none;
}

.radio_btn + label:before {
    border-radius: 50%;
}

.checkbox_btn:checked + label:before {
    background-color: #357fed;
    border-color: #357fed;
    color: #fff;
}

.radio_btn + label:after {
    width: 8px;
    height: 8px;
    background-color: transparent;
    position: absolute;
    top: 5px;
    left: 6px;
    border-radius: 50%;
    content: '';
    -webkit-transition: .3s all ease-in-out;
    -moz-transition: .3s all ease-in-out;
    -ms-transition: .3s all ease-in-out;
    -o-transition: .3s all ease-in-out;
    transition: .3s all ease-in-out;
}

.radio_btn:checked + label:after {
    background-color: #357fed;
}

.radio_btn:disabled + label:after {
    background-color: #d9dce2;
}

input:disabled + .slider:before {
    background-color: #d9dce2;
}

.switch {
    position: relative;
    display: inline-block;
    vertical-align: middle;
    width: 57px;
    height: 30px;
    border-radius: 15px;
    border: solid 1px #d9dce2;
    background-color: #fff;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #fff;
    -webkit-transition: .4s;
    -moz-transition: .4s;
    -ms-transition: .4s;
    -o-transition: .4s;
    transition: .4s;
    border-radius: 15px;
}

.switch input {
    display: none;
}

.slider.round:before {
    border-radius: 50%;
}

.slider:before {
    position: absolute;
    content: '\e82d';
    font-family: "Icons";
    font-size: 8px;
    height: 24px;
    width: 24px;
    left: 2px;
    top: 2px;
    background-color: #2d374c;
    -webkit-transition: .4s;
    -moz-transition: .4s;
    -ms-transition: .4s;
    -o-transition: .4s;
    transition: .4s;
    color: #fff;
    line-height: 24px;
    text-align: center;
}

.checked .slider:before {
    -webkit-transform: translateX(27px);
    -ms-transform: translateX(27px);
    transform: translateX(27px);
    background-color: #357fed;
    content: '\e823';
}

.border-block {
    border-radius: 4px;
    border: solid 2px #d9dce2;
}

.shadow-block {
    -webkit-box-shadow: 0 7px 14px 0 rgba(204, 207, 221, 0.3);
    -moz-box-shadow: 0 7px 14px 0 rgba(204, 207, 221, 0.3);
    box-shadow: 0 7px 14px 0 rgba(204, 207, 221, 0.3);
}

.shadow-block2 {
    -webkit-box-shadow: 0 27px 23px 0 rgba(167, 169, 178, 0.37);
    -moz-box-shadow: 0 27px 23px 0 rgba(167, 169, 178, 0.37);
    box-shadow: 0 27px 23px 0 rgba(167, 169, 178, 0.37);
    cursor: grabbing;
}

/*COLORS*/
.inform-block {
    border-radius: 4px;
    padding: 10px 37px 9px;
    position: relative;
}

.inform-block:before {
    position: absolute;
    z-index: 0;
    content: '';
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0.15;
    border-radius: 4px;
}

.btn-cancel {
    width: 9px;
    height: 9px;
    background: transparent;
    border: 0;
}

.inform-block .inform-block-icon {
    top: 10px;
    left: 12px;
    position: absolute;
    z-index: 1;
}

.shadow-block-drop {
    -webkit-box-shadow: 0 12px 23px 0 rgba(167, 169, 178, 0.55);
    -moz-box-shadow: 0 12px 23px 0 rgba(167, 169, 178, 0.55);
    box-shadow: 0 12px 23px 0 rgba(167, 169, 178, 0.55);
}

.inform-block .btn-cancel {
    top: 12px;
    right: 16px;
    position: absolute;
    z-index: 1;
    font-size: 10px;
}

.btn-cancel:before {
    content: '\e84d';
    font-family: "Icons";
}

.danger-bg-bef .btn-cancel, .danger-bg-bef .inform-block-icon {
    color: #e23d3d;
}

.inform-block.icon-check {
    font-size: 10px;
    top: 12px;
}

.success-bg-bef .btn-cancel, .success-bg-bef .inform-block-icon {
    color: #2be56d;
}

.info-bg-bef .btn-cancel, .info-bg-bef .inform-block-icon {
    color: #357fed;
}

.warning-bg-bef .btn-cancel, .warning-bg-bef .inform-block-icon {
    color: #ffac3c;
}

.page-section .inform-block {
    margin-top: -8px;
    margin-bottom: 30px;
}

.div {
    width: 110px;
    height: 110px;
    border-radius: 4px;
    display: inline-block;
    text-align: center;
    line-height: 110px;
    color: #fff;
}

.success-color {
    color: #2be56d !important;
}

.success-color .btn-carret {
    border-top-color: #2be56d !important;
}

.danger-color {
    color: #e23d3d !important;;
}

.warning-color {
    color: #ef981f !important;;
}

.doc-status-nav .btn.all-view-btn:before {
    display: none;
}

.success-bg, .success-bg-bef:before {
    background-color: #2be56d !important;
}

.favorite-bg, .favorite-bg-bef:before {
    background-color: #f4d949 !important;
}

.success-bg-2, .success-bg-bef-2:before {
    background-color: #2be56d !important;
}

.danger-bg, .danger-bg-bef:before {
    background-color: #e23d3d !important;
}

.warning-bg, .warning-bg-bef:before {
    background-color: #ef981f !important;
}

.info-bg, .info-bg-bef:before {
    background-color: #357fed !important;
}

.default-bg, .default-bg-bef:before {
    background-color: #1d273e !important;
}

.dark-bg, .dark-bg:before, .dark-bg-bef:before {
    background-color: #212b41 !important;
}

.grey-bg, .grey-bg:before {
    background-color: #8e94a7 !important;
}

.white-bg {
    border: solid 1px #d9dce2;
    background-color: #fff;
    color: #1d273e;
}

.ocean-bg {
    background-color: #f2f7ff;
    color: #1d273e;
}

.xs-text {
    font-size: 12px;
    line-height: 1.33;
    color: #1d273e;
}

.numb-style {
    font-family: 'Open Sans', sans-serif;
    font-size: 13px;
    font-weight: bold;
    line-height: 1.62;
    color: #357fed;
    -webkit-transition: .3s all;
    -moz-transition: .3s all;
    -ms-transition: .3s all;
    -o-transition: .3s all;
    transition: .3s all;
}

.sub-block .btn-nav-wrap {
    top: 1px;
    right: 0;
}

.form-control {
    height: 44px;
    border-radius: 4px;
    background-color: #f5f6fa;
    padding: 6px 12px;
    color: #1d273e;
    line-height: 1.5;
    font-size: 14px;
    border: 0;
    border: solid 1px #f5f6fa;
    -webkit-transition: .3s all;
    -moz-transition: .3s all;
    -ms-transition: .3s all;
    -o-transition: .3s all;
    transition: .3s all;
}

.white-form-control {
    background: #fff;
    color: #1d273e;
    border: solid 1px #d9dce2;
    height: 44px;
    border-radius: 4px;
    padding: 11px 17px;
    line-height: 1.5;
    font-size: 14px;
    -webkit-transition: .3s all;
    -moz-transition: .3s all;
    -ms-transition: .3s all;
    -o-transition: .3s all;
    transition: .3s all;
    display: block;
    width: 100%;
    padding-right: 42px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.white-form-control:hover {
    -webkit-box-shadow: inset 0 0 0 1px #d9dce2;
    -moz-box-shadow: inset 0 0 0 1px #d9dce2;
    box-shadow: inset 0 0 0 1px #d9dce2;
}

.white-form-control:focus {
    border-color: #357fed;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
}

.form-group .btn30 {
    position: absolute;
    top: 50%;
    margin-top: -15px;
    z-index: 1;
    right: 6px;
}

.clear-fild {
    font-size: 9px;
    opacity: 0;
    visibility: hidden;
    -webkit-transition: .3s all ease-in-out;
    -moz-transition: .3s all ease-in-out;
    -ms-transition: .3s all ease-in-out;
    -o-transition: .3s all ease-in-out;
    transition: .3s all ease-in-out;
}

.white-form-control__x + .clear-fild {
    opacity: 1;
    visibility: visible;
}

.white-form-control__x::-ms-clear {
    /* ÑƒÐ±Ð¸Ñ€Ð°ÐµÐ¼ Ñ€Ð¾Ð´Ð½Ð¾Ð¹ ÐºÑ€ÐµÑÑ‚Ð¸Ðº Ð¾Ñ‡Ð¸ÑÑ‚ÐºÐ¸ Ð² IE */
    display: none;
}

.page-visible-btn .btn i:before {
    font-size: 12px;
}

.form-group-wrap {
    padding-top: 28px;
    padding-bottom: 11px;
}

.input_group {
    position: relative;
}

.ava_block {
    width: 30px;
    height: 30px;
    border-radius: 4px;
    text-align: center;
    line-height: 30px;
    display: inline-block;
    vertical-align: top;
    font-size: 18px;
}

.ava_block img {
    display: block;
}

.ava_block.icon-profile {
    background-color: #357fed;
    color: #fff;
}

.st-block {
    width: 268px;
    height: 134px;
    border-radius: 4px;
    background-color: #357fed;
    padding: 10px;
    text-align: center;
    line-height: 114px;
}

.k-caption-year:after {
    content: '\e801';
    font-family: 'Icons';
    color: #357fed;
    font-weight: normal;
    font-size: 15px;
    line-height: 1;
    position: relative;
    top: 2px;
    margin-left: 5px;
}

/*intent-cell*/

.intent {
    width: 150px;
    /*height: 69px;*/
    border-radius: 4px;
    border: solid 1px #d9dce2;
    background-color: #fff;
    display: table;
    vertical-align: middle;
    padding: 3px;
    margin-bottom: 22px;
}

.intent-cell {
    display: table-cell;
    vertical-align: top;
}

.intent-cell:last-child {
    padding-left: 3px;
}

.zoom-2 .intent-cell:last-child {
    padding-left: 22px;
}

.zoom-3 .intent-cell:last-child {
    padding-left: 0;
}

.zoom-2 .btn-zoom-2 {
    display: block;
    float: left;
}

.intent .btn-nav, .intent .zoom-inner-2, .intent .intent-title, .intent .progress-title, .intent .sr-only, .intent .intent-info, .intent .btn-zoom-3, .intent .colap-up, .intent .btn-nav-wrap {
    display: none;
}

.zoom-2 .intent .colap-up, .zoom-2 .intent .btn-nav-wrap {
    display: block;
}

.intent .btn-nav-wrap {
    top: 11px;
    right: 19px;
}

.zoom-3.intent .btn-nav-wrap {
    position: absolute;
    top: 18px;
    right: 28px;
    /*z-index: 17;*/
}

.intent-title {
    font-size: 10px;
    font-weight: bold;
    line-height: 1.4;
    color: #357fed;
    margin: 15px 0 5px;
    text-transform: uppercase;
}

.progress-title {
    font-size: 10px;
    font-weight: 600;
    line-height: 1.4;
    color: #8c93a6;
    text-transform: uppercase;
    margin-bottom: 7px;
}

.sr-only {
    font-size: 14px;
    font-weight: bold;
    line-height: 1.5;
    font-family: 'Open Sans', sans-serif;
    left: 0;
}

.progress-bar-success .sr-only {
    color: #2be56d;
}

.intent-info-group {
    margin-bottom: 20px;
}

.intent-inner {
    display: table-cell;
    vertical-align: middle;
    padding: 3px 0;
    text-align: center;
    height: 57px;
    width: 128px;
}

.intent-inner a {
    color: #1d273e;
    text-decoration: underline;
    font-size: 10px;
    font-weight: 600;
    line-height: 1.4;
    color: #1d273e;
    text-transform: uppercase;
}

.intent {
    width: 305px;
    min-height: auto;
}

.ultimate_goal.intent .button-create {
    margin: 0 auto;
    width: 50%;
}

.ultimate_goal.intent .btn-zoom-2 {
    width: 90%;
}

.ultimate_goal.intent {
    text-align: center;
}

.ultimate_goal.intent .block-intent-title,
.ultimate_goal.intent .block-intent-title a {
    font-size: 18px;
    font-weight: bold;
}

.ultimate_goal.intent .block-intent-title {
    max-width: 760px;
}

.zoom-3 .ultimate_goal.intent .block-intent-title a,
.zoom-3 .ultimate_goal.intent .block-intent-title {
    font-size: 12px;
}

.ultimate_goal.intent {
    width: 800px;
}

.zoom-2 .intent-inner {
    display: none;
}

.block-intent-title-head, .block-intent-nav {
    padding-right: 10px;
}

.zoom-2 .intent .btn-nav, .zoom-2 .intent .zoom-inner-2 {
    display: block;
}

.zoom-3 .btn-pause {
    display: none;
}

.zoom-3 .block-intent-title {
    margin: 0;
}

.zoom-3 .block-intent-title-head {
    margin-bottom: 20px;
}

.intent.zoom-3 .progress {
    position: relative;
    left: 0;
    right: 0;
    bottom: auto;
    margin-top: 32px;
    overflow: visible;
    margin-bottom: 25px;
}

.zoom-3 .sr-only {
    top: -25px;
    overflow: visible;
    clip: auto;
}

.intent.zoom-3 .intent-title, .intent.zoom-3 .btn-zoom-3, .intent.zoom-3 .progress-title, .intent.zoom-3 .sr-only, .intent.zoom-3 .intent-info {
    display: block;
}

.intent.zoom-3 .btn-zoom-2 {
    display: none;
}

.zoom-3 {
    width: 748px;
    min-height: auto;
    padding: 15px 35px 35px;
}

.zoom-3.intent .colap-up {
    bottom: 35px;
    right: 30px;
}

.btn .icon-add {
    margin-right: 5px;
}

.intent.zoom-3 .block-intent-nav {
    position: absolute;
    bottom: 35px;
    left: 30px;
    right: 60px;
    z-index: 3;
    text-align: center;
}

.btn-pause {
    border-radius: 3px;
    background-color: #f2f7ff;
    width: 11px;
    color: #357fed;
    cursor: move;
    border: 0;
    font-size: 8px;
    font-weight: normal;
    height: 100%;
}

.btn-pause:before {
    font-family: 'Icons';
    content: '\e836';
}

.intent .info-label {
    margin-bottom: 5px;
}

.btn-zoom-2 {
    display: none;
}

.intent-info {
    max-height: 160px;
    overflow: hidden;
    position: relative;
}

.intent-info:before {
    background: -moz-linear-gradient(top, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, .8) 100%);
    background: -webkit-linear-gradient(top, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, .8) 100%);
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, .8) 100%);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#00ffffff', endColorstr='#ffffff', GradientType=0);
    position: absolute;
    top: 0;
    bottom: 0;
    z-index: 1;
    content: '';
    pointer-events: none;
    left: 0;
    width: 100%;
}

.block-progress div {
    display: flex;
    align-items: center;
}

.block-progress h4 {
    margin-right: 10px;
    font-size: 14px !important;
}

.block-progress div span {
    color: #697280;
}

.progress {
    height: 4px;
    margin-bottom: 0;
    overflow: hidden;
    background-color: #e5e9f3;
    border-radius: 2px;
    -webkit-box-shadow: none;
    box-shadow: none;
}

.progress-bar-success {
    background-color: #2be56d;
}

.progress-bar-danger {
    background-color: #d9534f;
}

.block-intent-title,
.block-intent-title a {
    font-size: 14px;
}

.block-intent {
    width: 305px;
    min-height: 172px;
    border-radius: 4px;
    border: solid 1px #d9dce2;
    background-color: #fff;
    padding: 18px 23px;
    position: relative;
    margin-bottom: 20px;
}

.block-intent-title-head > div {
    display: table-cell;
    vertical-align: top;
    width: 100%;
}

.zoom-3 .block-intent-title-head, .zoom-3 .block-intent-nav {
    padding-right: 0;
}

.block-intent-title-head .btn-nav-wrapper {
    float: right;
}

.btn-zoom-3 {
    float: left;
    text-align: center;
    width: calc(100% - 30px);
}

.block-intent-nav-2 {
    margin-bottom: 22px;
    display: none;
    padding-right: 18px;
}

.block-intent-nav-2:after, .block-intent-nav-3:after {
    display: table;
    clear: both;
    content: "";
}

.block-intent-nav-3 {
    display: none;
}

.zoom-2 .block-intent-nav-2 {
    display: block;
}

.zoom-3 .block-intent-nav-2 {
    display: none;
}

.zoom-3 .block-intent-nav-3 {
    display: block;
}

.block-intent-title {

    margin-bottom: 30px;
    /*max-height: 63px;*/
    /*overflow: hidden;*/
    margin-top: 15px;
}

.page-title-wrap + .main-btn-menu-wrap {
    position: absolute;
    top: 24px;
    right: 33px;
}

.btn-nav-wrap {
    top: 0;
    right: 0;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    position: relative;
}

.opened_menu .page-section {
    /*width: calc(100% - 327px);*/
    /*padding-right: 49px;*/
}

.datepicker {
    max-width: 222px;
}

.dropdown-menu {
    border-radius: 4px;
    -webkit-box-shadow: 0 12px 23px 0 rgba(167, 169, 178, 0.55);
    -moz-box-shadow: 0 12px 23px 0 rgba(167, 169, 178, 0.55);
    box-shadow: 0 12px 23px 0 rgba(167, 169, 178, 0.55);
    background-color: #fff;
    border: 0;
    right: 20px;
}

.user-nav .dropdown-menu {
    left: auto;
    margin-top: 12px !important;
    right: 0px;
}

.action-button-gray .dropdown-menu {
    right: 0;
    left: auto;
    width: 202px;

}

.user-nav.open .btn-carret {
    background-color: #f2f7ff;
    color: #357fed;
}

.user-nav.open .btn-carret:before {
    content: '\e801';
}

table tr th.sort-header-label a:before,
table tr th.sort-header-label a.desc:before {
    position: absolute;
    top: 10px;
    right: 16px;
    font-family: 'Icons';
    content: '\e802';
}
table tr th.sort-header-label a:after,
table tr th.sort-header-label a.desc:after {
    position: absolute;
    top: 10px;
    right: 25px;
    font-family: 'Icons';
    content: '\e801';
    color:#CBD3E0;
}

table tr th.sort-header-label a.asc:before {
    position: absolute;
    top: 10px;
    right: 25px;
    content: '\e801';
    font-family: 'Icons';
}

table tr th.sort-header-label a.asc:after {
    position: absolute;
    top: 10px;
    right: 16px;
    content: '\e802';
    font-family: 'Icons';
    color:#CBD3E0;
}

.btn-nav-wrap .dropdown-menu {
    padding: 11px 17px;
    margin: 5px 0 0;
    min-width: 220px;
    right: -2px;
    left: auto;
}

.btn-nav-wrap .dropdown-menu ul {
    list-style: none;
    margin: 0;
}

.dropdown-menu ul li {
    padding-left: 0;
}

.dropdown-menu ul li:before {
    display: none;
}

.btn-nav-wrap .dropdown-menu ul li {
    margin-bottom: 13px;
}

.btn-nav-wrap .dropdown-menu ul li:last-child {
    margin-bottom: 6px;
}

.btn-nav-wrap .dropdown-menu ul a {
    font-size: 14px;
    font-weight: normal;
    color: #1d273e;
    text-decoration: underline;
}

.btn-nav-wrap .dropdown-menu ul a:hover {
    color: #357fed;
}

.btn-nav {
    color: #2d374c;
    border: 0;
    background: #fff;
    font-size: 12px;
    font-weight: normal;
    -webkit-transition: .3s all;
    -moz-transition: .3s all;
    -ms-transition: .3s all;
    -o-transition: .3s all;
    transition: .3s all;
    width: 29px;
    height: 29px;
    border-radius: 50%;
    transform: rotate(90deg);
}

.btn-nav:hover {
    color: #357fed;
    background-color: #f5f6fa;
}

.btn-nav:before {
    font-family: 'Icons';
    content: '\e800';
}

.block-intent-nav:after {
    display: table;
    clear: both;
    content: '';
}

.colap-up {
    border-radius: 4px;
    border: solid 1px #d9dce2;
    background-color: transparent;
    width: 30px;
    height: 30px;
    color: #2d374c;
    font-weight: normal;
    font-size: 7px;
    -webkit-transition: .3s all;
    -moz-transition: .3s all;
    -ms-transition: .3s all;
    -o-transition: .3s all;
    transition: .3s all;
}

.colap-up:hover {
    background-color: #f5f6fa;
    color: #2d374c;
}

.colap-up.collapsed {
    background-color: #357fed;
    border-color: #357fed;
    color: #fff;
}

.colap-up.collapsed:hover {
    opacity: .8;
}

.colap-up:before {
    font-family: 'Icons';
    content: '\e82f';
    display: inline-block;
}

.colap-up.collapsed:before {
    font-family: 'Icons';
    content: '\e82e';
    display: inline-block;
}

/*.colap-up.collapsed:before{
    -webkit-transform: rotate(180deg);
    -moz-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    -o-transform: rotate(180deg);
    transform: rotate(180deg);
}*/
.sub-block-wrap {
    padding: 0 22px;
}

.sub-block {
    padding: 12px 0 16px;
    border-bottom: solid 1px #d9dce2;
    position: relative;
}

.btn-nav-pad {
    padding-right: 40px !important;
}

.sub-block .info-label {
    margin: 0 0 2px;
}

.sub-block:last-child {
    border-bottom: 0;
}

.date-label {
    width: 72px;
    border-radius: 4px;
    background-color: #f2f7ff;
    font-family: 'Open Sans', sans-serif;
    font-size: 11px;
    font-weight: 600;
    line-height: 1.1;
    color: #357fed;
    padding: 6px;
    text-align: center;
}

.sub-block .date-label {
    position: absolute;
    top: 12px;
    left: 0;
    z-index: 1;
}

.date-label-block {
    padding-left: 88px;
}

.icon-label {
    width: 30px;
    border-radius: 4px;
    background-color: #f2f7ff;
    font-size: 11px;
    font-weight: 600;
    line-height: 1.1;
    color: #357fed;
    padding: 6px;
    text-align: center;
}

.sub-block .icon-label {
    position: absolute;
    top: 12px;
    left: 0;
    z-index: 1;
}

.icon-label-block {
    padding-left: 40px;
}

.page-head-wrap {
    margin-bottom: 22px;
}

.page-head-wrap:after {
    display: table;
    clear: both;
    content: '';
}

.page-head-wrap .section-title {
    margin: 0;
}

.page-head-left {
    float: left;
}

.page-head-right {
    float: right;
}

.icon-doc {
    font-size: 15px;
    margin-right: 10px;
}

.ministry {
    margin-top: 10px;
}

.ministry a:hover {
    text-decoration: none;
}

.ministry:hover .icon-doc {
    color: #357fed;
}

/*nav-menu*/
.page-wrapper {
    transition: margin 0.3s;
}

.opened_menu .page-wrapper {
    margin-left: 300px;
}

.opened_menu .nav-menu {
    left: 0;
    opacity: 1;
}

.nav-menu {
    width: 327px;
    border-left: solid 1px #d9dce2;
    position: fixed;
    top: 64px;
    bottom: 0;
    right: -100%;
    z-index: 99;
    padding: 5px 0 120px;
    /*opacity: 0;*/
    -webkit-transition: .05s right .05s;
    -moz-transition: .05s right .05s;
    -ms-transition: .05s right .05s;
    -o-transition: .05s right .05s;
    transition: .05s right .05s;
    background: #fff;
}

.nav-menu-inner {
    max-height: calc(100vh - 184px);
    max-height: -webkit-calc(100vh - 184px);
    max-height: -o-calc(100vh - 184px);
    overflow-y: auto;
}

.no-footer .nav-menu-inner {
    max-height: calc(100vh - 69px);
    max-height: -webkit-calc(100vh - 69px);
    max-height: -o-calc(100vh - 69px);
    overflow-y: auto;
}

.nav-menu-foot {
    position: absolute;
    bottom: 30px;
    right: 30px;
    z-index: 5;
}

.nav-menu-inner-block {
    border-bottom: solid 1px #d9dce2;
    padding: 28px 28px 15px 28px;
}

.collapse-inner {
    border-bottom: solid 1px #d9dce2;
    padding: 28px 60px 20px 28px;
    position: relative;
}

.collapse-inner-block {
    max-height: 106px;
    overflow: hidden;
    position: relative;
    -webkit-transition: .3s all;
    -moz-transition: .3s all;
    -ms-transition: .3s all;
    -o-transition: .3s all;
    transition: .3s all;
}

.opened .collapse-inner-block {
    max-height: 300px;
}

.opened .collapse-inner-block:before {
    display: none;
}

.collapse-inner-block:before {
    background: -moz-linear-gradient(top, rgba(255, 255, 255, 0) 40%, rgba(255, 255, 255, 1) 100%);
    background: -webkit-linear-gradient(top, rgba(255, 255, 255, 0) 40%, rgba(255, 255, 255, 1) 100%);
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0) 40%, rgba(255, 255, 255, 1) 100%);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#00ffffff', endColorstr='#ffffff', GradientType=0);
    position: absolute;
    top: 0;
    bottom: 0;
    z-index: 1;
    content: '';
    pointer-events: none;
    left: 0;
    width: 100%;
}

.collapse-inner .colap-up {
    bottom: 20px;
    position: absolute;
    right: 20px;
}

.collapse-inner .btn {
    position: relative;
    z-index: 5;
}

/*.block .xs-title{
    margin-bottom: 20px;
}*/
.nav-menu-inner-block .info-label, .collapse-inner .info-label {
    margin: 0 0 19px;
}

.collapse-head {
    padding-left: 32px;
    position: relative;
}

.nav-menu-inner-block p {
    margin-bottom: 18px;
}

.nav-menu-inner-block p {
    margin-bottom: 5px;
}

.collapse-head .collapse_btn {
    position: absolute;
    top: 1px;
    left: 0;
    z-index: 2;
}

.no-border,
table.no-border tr th,
table.no-border tr td {
    border: 0 !important;
}

.collapse_btn {
    width: 17px;
    height: 17px;
    border-radius: 4px;
    border: solid 1px #d9dce2;
    background-color: #fff;
    text-decoration: none;
    font-size: 5px;
}

.collapse_btn:before {
    font-family: 'Icons';
    content: '\e82e';
    color: #357fed;
}

.collapse_btn.collapsed:before {
    content: '\e82f';
    color: #1d273e;
}

.nav-menu-inner a:not([class]) {
    font-size: 14px;
    font-weight: normal;
    line-height: 1.5;
    color: #1d273e;
    display: inline-block;
    text-decoration: underline;
}

.nav-menu-inner a:not([class]):hover {
    color: #357fed;
}

.metod-drop {
    position: absolute;
    width: 100%;
    border-radius: 4px;
    -webkit-box-shadow: 0 8px 18px 0 rgba(167, 169, 178, 0.39);
    -moz-box-shadow: 0 8px 18px 0 rgba(167, 169, 178, 0.39);
    box-shadow: 0 8px 18px 0 rgba(167, 169, 178, 0.39);
    background-color: #fff;
    z-index: 5;
    top: auto;
    bottom: 100%;
    margin: 0 0 24px;
    padding: 24px 0 11px;
    display: none;
}

.metod-drop-body {
    padding: 0 28px 11px;
    border-bottom: solid 1px #d9dce2;
}

.metod-drop-body p {
    margin-bottom: 15px;
}

.metod-drop-foot {
    padding: 6px 28px 0;
}

.metod-drop:after {
    top: 100%;
    left: 50%;
    border: solid transparent;
    content: " ";
    height: 0;
    width: 0;
    position: absolute;
    pointer-events: none;
    border-color: rgba(255, 255, 255, 0);
    border-top-color: #fff;
    border-width: 10px;
    margin-left: -10px;
}

.tr-link {
    font-size: 10px;
    font-weight: 600;
    line-height: 1.4;
    color: #1d273e;
    text-transform: uppercase;
    text-decoration: underline;
}

.tr-link:hover {
    color: #357fed;
}

.metod-drop table td {
    padding: 5px 0;
    border: 0;
}

.metod-drop table td:last-child {
    text-align: right;
}

.drop-close {
    width: 30px;
    height: 30px;
    border-radius: 0 4px 0 4px;
    position: absolute;
    top: 0;
    right: 0;
    z-index: 5;
    border: 0;
    color: #fff;
    font-size: 10px;
}

.drop-close:hover {
    opacity: .8;
}

.drop-close:before {
    font-family: "Icons";
    content: '\e82d';
}

.drop-close.success-bg:before {
    content: '\e823';
}

.os-font {
    font-family: 'Open Sans', sans-serif;
    font-size: 14px;
    line-height: 1.5;
}

.hide-nav-intent {
    width: 30px;
    height: 30px;
    border-radius: 4px;
    border: solid 1px #d9dce2;
    background-color: #fff;
    position: absolute;
    top: 0;
    right: 0;
    z-index: 5;
    font-size: 10px;
    color: #1d273e;
}

.hide-nav-intent:hover {
    color: #357fed;
    background-color: #f5f9ff;
    border-color: #357fed;
}

.hide-nav-intent:before {
    font-family: "Icons";
    content: '\e82d';
}

.nav-intent {
    position: relative;
    padding-right: 45px;
}

/*LOGIN*/
body.auth-body {
    height: 100%;
    padding-bottom: 0;
}

.auth-sidebar {
    background-color: #fff;
    /*position: fixed;*/
    top: 0;
    left: 0;
    height: 100%;
    width: 360px;
    z-index: 3;
    color: #1d273e;
    padding: 30px 29px;
}

.auth-head {
    margin-bottom: 30px;
}

.auth-head .btn-default {
    min-width: 135px;
}

.auth-head:after {
    display: table;
    clear: both;
    content: '';
}

.auth-footer {
    position: absolute;
    bottom: 30px;
    left: 29px;
    right: 29px;
    z-index: 5;
}

.auth-bg {
    position: fixed;
    top: 0;
    right: 0;
    /*left: 360px;*/
    height: 100%;
    width: calc(100% - 327px);
    z-index: 2;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: 50% 26%;
}

.circle-default {
    border-color: #d9dce2;
}

.circle-default, .circle-default:active, .circle-default:visited, .circle-default:focus {
    color: #357fed;
    background: #fff;
}

.auth-body .circle-default {
    border-color: #357fed;
}

.circle-default:hover {
    color: #357fed;
    background: #f5f9ff;
    border-color: #357fed;
}

.auth-block {
    /*display: table;*/
    width: 100%;
    /*height: calc(100% - 180px);*/
    vertical-align: middle;
}

.auth-block-inner {
    /*display: table-cell;*/
    vertical-align: middle;
    padding-top: 60px;
}

.auth-block .section-title {
    margin: 0 0 17px;
}

.auth-block p {
    margin-bottom: 17px;
}

label, .info-label {
    font-size: 10px;
    font-weight: 600;
    line-height: 1.4;
    color: #A4B1C8;
    text-transform: uppercase;
    margin: 0 0 9px;
}


.form-group {
    margin-bottom: 10px;
    position: relative;
}

.form-group-lg {
    margin-top: 31px;
    margin-bottom: 31px;
}

.auth-block .btn-link {
    margin-top: 3px;
    display: inline-block;
}

.help-block {
    /*margin-top: 0;*/
    /*margin-bottom: 0;*/
    /*position: absolute;*/
    /*top: 0;*/
    background: #fff;
    font-size: 12px;
    font-weight: 200;
    /*line-height: 1.9;*/
    z-index: 2;
    /*text-transform: uppercase;*/
}

.help-block ul {
    margin: 0;
}

.has-error .form-control {
    -webkit-box-shadow: none;
    box-shadow: none;
    border-color: #e23d3d;
    outline: none;
    background-color: #fffafa;
}

/*CABINET*/

/*
.header{
    padding: 17px 9px 0;
    background: #fff;
    border-bottom: solid 1px #d9dce2;
    position: fixed;
    z-index: 100;
    left: 0;
    width: 100%;
    top: 0;
    height: 64px;
}*/

.header-wrap:after {
    display: table;
    clear: both;
    content: '';
}

/*
.header-left{
    float: left;
}
*/
/*
.header-right{
    float: right;
}

.header-right .btn + .btn{
    margin-left: 5px;
}
.no-autoriz .header-right .btn{
    min-width: 150px;
}
.header-right nav, .header-right .search-form, .header-right .user-nav{
    display: inline-block;
    vertical-align: top;
}
*/
/*
.main-menu{
    list-style: none;
    margin: 4px 0 0;
}
.main-menu li{
    display: inline-block;
    margin-right: 29px;
}
.main-menu li a{
    font-size: 10px;
    font-weight: 600;
    line-height: 1.4;
    color: #8c93a6;
    text-transform: uppercase;
    display: inline-block;
    padding-bottom: 20px;
    border-bottom: 3px solid transparent;
    text-decoration: none;
}
.main-menu li.current-menu-item a, .main-menu li a:hover{
    color: #357fed;
    border-color: #357fed;
    text-decoration: none;
}
.main-menu li.current-menu-item a{
    font-weight: bold;
}
*/
.search-form {
    position: relative;
}

.search-form .form-control {
    height: 30px;
    width: 244px;
    padding-left: 27px;
    font-size: 10px;
    font-weight: 600;
    line-height: 1.9;
    text-transform: uppercase;
    border: solid 1px #d9dce2;
    background: #fff;
}

.header .search-form .btn-search {
    color: #1d273e;
}

.search-form .btn-search {
    position: absolute;
    top: 6px;
    left: 9px;
    z-index: 5;
    color: #9aa6b8;
}

.btn-search {
    background: transparent;
    border: 0;
    font-size: 12px;
}

.btn-search:hover {
    color: #357fed;
}

.btn-search:before {
    font-family: "Icons";
    content: '\e835';
}

.user-nav-ava, .user-nav-ava-img, .user-nav-ava img {
    width: 32px;
    height: 32px;
}

.user-nav-ava, .user-nav-ava-img{
    border-radius: 50%;
}

.user-nav-ava-img{
    overflow: hidden;
}

.user-nav-ava {
    cursor: pointer;
    position: relative;
    display: flex;
    align-items: center;
    justify-items: center;
    float: left;
    margin-right: 1px;
    background: #F2F7FF;
}

.user-nav-ava:after {
    position: absolute;
    content: '';
    display: block;
    width: 8px;
    height: 8px;
    bottom: 0px;
    right: 0px;
    border-radius: 8px;
    background-color: #357FED;
    box-shadow: inset 0px 0px 0px 1px white;
}

.user-nav-ava span{
    font-size: 13px;
    font-style: normal;
    font-weight: 600;
    line-height: 20px; 
    color: #357FED;
}
.circle-progress {
    width: 160px;
    height: 160px;
    position: relative;
    display: inline-block;
    vertical-align: top;
}

.circle-progress svg {
    width: 160px;
    height: 160px;
    /*position: absolute;*/
    top: 0;
    left: 0;
    z-index: 1;
}

.circle-progress-block {
    width: 110px;
    height: 110px;
    position: absolute;
    top: 24px;
    left: 24px;
    z-index: 2;
    background: #fff;
    border-radius: 50%;
    text-align: center;
    font-size: 12px;
    line-height: 1.75;
    padding: 40px 30px 0;
}

.circle-progress-count {
    font-family: 'Open Sans', sans-serif;
    font-size: 20px;
    font-weight: 600;
    font-style: normal;
    font-stretch: normal;
    line-height: 1;
    color: #1d273e;
}

.circle-progress-wrap {
    text-align: center;
}

.icon-star:before {
    color: #f4d949;
    font-size: 14px;
    font-weight: normal;
}

/*.colap-up{
    position: absolute;
    z-index: 3;
    bottom: 23px;
    right: 20px;
}*/
.intent .colap-up {
    float: right;
}

.top-intent-block-inner {
    max-width: 560px;
    margin: 0 auto;
    text-align: center;
}

.top-intent-title {
    margin-bottom: 25px;
}

.header .search-form {
    margin-right: 7px;
}

/*.btn-carret:before{
    border: 0;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 5px solid #1d273e;
    background: transparent;
    content: '';
}*/
.user-nav {
    position: relative;
}

.btn-carret {
    width: 29px;
    height: 29px;
    color: #1d273e;
    border-radius: 50%;
    border: 0;
    background: transparent;
    font-size: 15px;
}

.btn-carret:before {
    content: '\e801';
    font-family: 'Icons';
}

.btn-carret:hover {
    background-color: #f2f7ff;
    color: #357fed;
}

.btn i {
    margin-right: 5px;
    font-style: normal;
}

.navigation{
    overflow: hidden;
    padding: 8px 0px 16px;
    display: flex;
    /* margin: 0px -12px; */
    flex-wrap: wrap;
    justify-content: space-between;
    /* grid-template-columns: max-content 1fr max-content; */
}

.navigation__divider{
    width: 1px;
    height: 24px;
    background-color: #C1C4D0;
    margin: 6px 0px;
}

.navigation__left-group{
    display: flex;
    gap: 24px;
    padding-right: 49px; /* 24 + 25 to hide divider */
}

.navigation__right-group{
    display: flex;
    gap: 24px;
    /* padding: 0px 12px 0px 0px; */
}

.navigation__center-group{
    position: relative;
    flex-grow: 1;
    display: flex;
    gap: 24px;
    margin-left: -25px;
    padding: 0px 24px 0px 0px;
}

/* .navigation__center-group::before{
    content: '';
    width: 1px;
    height: 24px;
    background-color: #C1C4D0;
    margin: 6px 0px;
    position: absolute;
    left: -12px;
} */

.navigation-button{
    display: flex;
    padding: 8px 0px;
    gap: 8px;
    border: 1px solid transparent;
    border-radius: 4px;
}

.navigation-button:hover .navigation-button__icon{
    color: #357FED;
}

.navigation-button:hover .navigation-button__text{
    color: #357FED;
}

.navigation-button__text{
    transition: color 0.3s ease-in-out, background-color 0.3s ease-in-out;
    color:#667080;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 18px;
    margin: 0px;
}

.navigation-button__icon{
    transition: color 0.3s ease-in-out, background-color 0.3s ease-in-out;
    /* padding: 8px 12px; */
    color: #667080;
    font-size: 14px;
    line-height: 18px;
}

.navigation-button__badge{
    width: 16px;
    height: 16px;
    border-radius: 16px;
    background-color: #DF2323;
    line-height: 16px;
    text-align: center;
    font-size: 10px;
    font-style: normal;
    font-weight: 700;
    color: white;
    margin: 1px 0px 1px -4px;
}

.navigation__left-group .navigation-button:first-child.navigation-button_active{
    margin-left: 0px;
}

.navigation__left-group .navigation-button:last-child.navigation-button_active{
    margin-right: 0px;
}

.navigation__right-group .navigation-button:first-child.navigation-button_active{
    margin-left: 0px;
}

.navigation__right-group .navigation-button:last-child.navigation-button_active{
    margin-right: 0px;
}

.navigation__center-group .navigation-button:nth-child(2).navigation-button_active{
    margin-left: 0px;
}

.navigation-button_active{
    padding: 8px 12px;
    margin: 0px -12px;
    background-color: white;
    border: 1px solid #E8E9ED;
}

.navigation-button_active .navigation-button__icon{
    color: #1D273E;
}

.navigation-button_active .navigation-button__text{
    color: #1D273E;
}

.page-visible-btn .btn {
    color: #1d273e;
    background: transparent;
    margin: 0 1px;
}

.page-visible-btn .btn span {
    text-decoration: underline;
}

.page-visible-btn .btn.active {
    color: #357fed;
    background-color: #fff;
}

.page-visible-btn .btn:hover {
    color: #357fed;
    background-color: #fff;
    border: 1px solid #b7c0d7;
}

.page-visible-btn .btn.active span, .page-visible-btn .btn:hover span {
    text-decoration: none;
}

.user-nav-menu ul li {
    white-space: nowrap;
    padding: 0 18px;
}

.user-nav-menu li i::before {
    width: 24px;
}

.user-nav-menu li i {
    margin-right: 4px;
    font-size: 15px;
}

.user-nav-menu li a:hover {
    text-decoration: none;
}

.method-list-block .numb-style {
    margin-left: 5px;
    display: inline-block;
}

.method-list-block a:hover .numb-style {
    color: #fff;
}

.btn-type2 {
    padding: 10px 12px;
    color: #1d273e !important;
    text-decoration: underline;
}

.on-hover .dropdown-menu {
    margin-bottom: 20px;
    padding: 10px;
    top: auto;
    bottom: 100%;
}

.dropdown-menu-2 {
    padding: 10px;
    -webkit-box-shadow: 0 4px 8px 0 rgba(204, 207, 221, 0.16);
    -moz-box-shadow: 0 4px 8px 0 rgba(204, 207, 221, 0.16);
    box-shadow: 0 4px 8px 0 rgba(204, 207, 221, 0.16);
}

.on-hover .dropdown-menu:before {
    position: absolute;
    top: 100%;
    left: 50%;
    border-left: 7px solid transparent;
    border-right: 7px solid transparent;
    border-top: 7px solid #fff;
    content: '';
    margin-left: -7px;
}

.on-hover {
    position: relative;
}

/*page-section*/
.breadcrumb {
    padding: 0;
    margin-bottom: 15px;
    background-color: transparent;
    border-radius: 0;
}

.breadcrumb li {
    font-size: 10px;
    font-weight: 600;
    line-height: 1;
    color: #1d273e;
    text-transform: uppercase;
}

.breadcrumb a {
    font-size: 10px;
    text-decoration: underline;
    font-weight: 600;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.9;
    color: #8c93a6;
}

.breadcrumb a:hover {
    color: #8c93a6;
}

.breadcrumb > li:after {
    margin: 0px 11px 0 14px;
    color: #d9dce2;
    content: "\2022";
    display: inline-block;
    font-size: 16px;
    position: relative;
    top: 1px;
}

.breadcrumb > li + li:before {
    display: none;
}

.breadcrumb > li:last-child:after {
    display: none;
}

.breadcrumb > .active {
    color: #1d273e;
}

.page-section {
    padding-top: 70px;
    background-color: #f5f6fa;
    min-height: 100vh;
    position: relative;
    -webkit-transition: width .3s;
    -moz-transition: width .3s;
    -ms-transition: width .3s;
    -o-transition: width .3s;
    transition: width .3s;
    width: 100%;
}

.page-section.fullscreen {
    margin-top: 0 !important;
}

.page-head {
    position: relative;
    padding-right: 210px;
}

.page-visible-btn {
    border-radius: 4px;
    /*border: solid 1px #d9dce2;*/
    padding: 4px;
    margin-bottom: 10px;
    z-index: 5;
    display: inline-block;
    background-color: rgba(217, 220, 226, 0.5);
}

.page-edit-btn .btn {
    border: 1px solid #b7c0d7;
}

.page-edit-btn {
    padding: 4px;
    margin-bottom: 10px;
    z-index: 5;
    display: inline-block;

}

.page-visible-btn.center {
    position: absolute;
    left: 0;
    right: 0;
    /*width: fit-content;
    max-width: 420px;*/
    width: 363px;
    margin-left: auto;
    margin-right: auto;
}

.page-visible-btn-right {
    padding: 0;
    border-radius: 4px;
    margin-bottom: 10px;
    z-index: 5;
    display: inline-block;
    box-shadow: 0 7px 17px 0 rgba(204, 207, 221, 0.3);
    background-color: #ffffff;
}

.page-visible-btn-right a {
    padding-left: 9px;
    font-size: 11px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1;
    color: #1d273e;
    text-transform: unset;
}

.page-visible-btn-right a span {
    display: inline-block;
    width: 24px;
    height: 24px;
    margin: 0 8px 0 0;
    padding: 7px 6px 5px;
    border-radius: 4px;
    background-color: #f2f7ff;
}

.page-visible-btn-right a span b {
    font-size: 12px;
    width: 12px;
    height: 12px;
    background-color: #357fed;
    display: inline-block;
    color: #ffffff;
    border-radius: 50%;
    -moz-border-radius: 50%;
    -webkit-border-radius: 50%;
}

.page-visible-btn-right a span b i {
    font-size: 8px;
}

.page-visible-btn-right .btn {
    background-color: #fff;
}

.page-empty-buttons {
    height: 25px;
    width: 100%;
}

.block {
    /*border-radius: 4px;*/
    /*border: solid 1px #d9dce2;*/
    background-color: #fff;
    position: relative;
    /*padding: 23px 22px;*/
    -webkit-box-shadow: inset 0 0 0 1px #fff;
    -moz-box-shadow: inset 0 0 0 1px #fff;
    /*box-shadow: inset 0 0 0 1px #fff;*/
    -webkit-transition: .3s box-shadow, .3s border-color;
    -moz-transition: .3s box-shadow, .3s border-color;
    -ms-transition: .3s box-shadow, .3s border-color;
    -o-transition: .3s box-shadow, .3s border-color;
    transition: .3s box-shadow, .3s border-color;
    margin-bottom: 22px;
    border-radius: 4px;
    box-shadow: 0 7px 17px 0 rgba(204, 207, 221, 0.3);

}

/*.block:hover{
    border: solid 1px #357fed;
    -webkit-box-shadow: inset 0 0 0 1px #fff;
    -moz-box-shadow: inset 0 0 0 1px #fff;
    box-shadow: inset 0 0 0 1px #fff;
}*/
.block .no-hover:hover {
    border-color: #357fed;
    -webkit-box-shadow: inset 0 0 0 1px #357fed;
    -moz-box-shadow: inset 0 0 0 1px #357fed;
    box-shadow: inset 0 0 0 1px #357fed;
}

.page-head .section-title {
    margin: 0 0 24px;
}

.top-intent .info-label {
    margin-bottom: 16px;
}

.main-btn-menu .btn-menu {
    transform: rotate(90deg);
    /* margin-top: 3px; */
}

.btn-menu {
    transition: .15s;
    width: 32px;
    height: 32px;
    /* -webkit-box-shadow: 0 7px 14px 0 rgba(204, 207, 221, 0.3);
    -moz-box-shadow: 0 7px 14px 0 rgba(204, 207, 221, 0.3);
    box-shadow: 0 7px 14px 0 rgba(204, 207, 221, 0.3); */
    background-color: #357FED;
    z-index: 105;

    color: #fff;
    border: 0;
    border-radius: 50%;
    font-size: 10px;
    font-weight: 700;
}

.btn-menu:hover {
    transition: .15s;
    background-color: #357FED;
    color: #fff;
}

.btn-menu:active {
    transition: .15s;
    background-color: #1C4789;
}

.commitment-edit-btn {
    width: 34px;
    height: 34px;
    -webkit-box-shadow: 0 7px 14px 0 rgba(204, 207, 221, 0.3);
    -moz-box-shadow: 0 7px 14px 0 rgba(204, 207, 221, 0.3);
    box-shadow: 0 7px 14px 0 rgba(204, 207, 221, 0.3);
    background-color: #fff;
    z-index: 9;
    display: flex;
    color: #357fed;
    border: 0;
    border-radius: 50%;
    font-size: 10px;
    font-weight: 700;
    align-items: center;
    margin-left: 45px;
    margin-top: -24px;
    /*position: absolute;*/
}

.commitment-edit-btn a {
    width: 100%;
    text-align: center;
}

.commitment-edit-btn:hover {
    background-color: #357fed;
    color: #fff;
}

.commitment-edit-btn:hover a {
    color: #fff;
}

.btn-menu:before {
    font-family: "Icons";
    content: '\e800';
}

.opened_menu .btn-menu {
    right: 310px;
}

.opened_menu .btn-sidebar {
    transform: rotate(180deg);
    transition: all 0.3s;
}

.btn-sidebar:before {
    content: '\e838';
    font-weight: normal;
    font-size: 12px;
}

/*methodology*/
.nav-menu-head {
    border-bottom: solid 1px #d9dce2;
    padding: 28px 28px 23px 28px;
}

.method-list .method-list-block {
    counter-increment: step-counter;
    position: relative;
}

.method-list-block a {
    display: block;
    padding: 18px 28px 18px 60px;
    text-decoration: underline;
    color: #1d273e;
    border-bottom: solid 1px #d9dce2;
    position: relative;
}

.method-list .method-list-block:before {
    content: "0" counter(step-counter);
    width: 20px;
    height: 20px;
    border-radius: 4px;
    background-color: #f5f6fa;
    font-family: 'Open Sans', sans-serif;
    font-size: 8px;
    font-weight: bold;
    color: #8c93a6;
    display: inline-block;
    position: absolute;
    text-align: center;
    line-height: 20px;
    left: 27px;
    top: 19px;
    -webkit-transition: all .3s;
    -moz-transition: all .3s;
    -ms-transition: all .3s;
    -o-transition: all .3s;
    transition: all .3s;
    z-index: 3;
}

.method-list {
    max-height: calc(100vh - 150px);
    max-height: -webkit-calc(100vh - 150px);
    max-height: -o-calc(100vh - 150px);
    overflow-y: auto;
}

.method-list-block a:hover {
    background-color: #357fed;
    color: #fff;
    text-decoration: none;
    border-bottom-color: #357fed;
}

.method-list-block a:after {
    position: absolute;
    top: 0;
    left: 0;
    top: -1px;
    width: 100%;
    height: 3px;
    content: '';
    background: transparent;
    -webkit-transition: all .3s;
    -moz-transition: all .3s;
    -ms-transition: all .3s;
    -o-transition: all .3s;
    transition: all .3s;
}

.method-list-block a:hover:after {
    background: #357fed;
}

.method-list-block:hover:before {
    background-color: #fff;
    color: #357fed;
}

.content p {
    margin-bottom: 20px;
}

.content ul:not([class]) {
    list-style: none;
    margin-bottom: 20px;
}

.content ul:not([class]) li {
    padding-left: 14px;
    position: relative;
    margin-bottom: 5px;
}

.content ul:not([class]) li:before {
    position: absolute;
    width: 4px;
    height: 4px;
    background-color: #357fed;
    border-radius: 50%;
    left: 0;
    top: 8px;
    content: '';
}

.methodology h2 {
    margin-top: 40px;
}

.metod-briefly {
    padding: 28px;
    max-height: calc(100% - 65px);
    overflow: hidden;
}

.counter-block {
    position: relative;
    padding-left: 32px;
}

.count-label {
    width: 20px;
    height: 20px;
    border-radius: 4px;
    background-color: #f5f6fa;
    font-family: 'Open Sans', sans-serif;
    font-size: 8px;
    font-weight: bold;
    color: #8c93a6;
    display: inline-block;
    position: absolute;
    text-align: center;
    line-height: 20px;
    left: 0;
    top: 0;
}

.empty-block {
    border: dashed 1px rgba(140, 147, 166, .5);
    padding: 42px 48px;
    margin-bottom: 30px;
    display: table;
    width: 100%;
}

.empty-block > div {
    display: table-cell;
    vertical-align: middle;
}

.empty-block > div:last-child {
    text-align: right;
}

.half-empty-block > div {
    text-align: center;
    display: block;
}

.half-empty-block > div:last-child {
    text-align: center;
    margin-top: 34px;
}

.empty-block p {
    margin-bottom: 34px;
}

.page-section .search-form {
    margin-bottom: 20px;
}

.white-control {
    height: 48px;
    border-radius: 4px;
    border: solid 1px #d9dce2;
    background-color: #fff;
    width: 100%;
    padding: 12px 20px 12px 42px;
}

.search-form.lg-search .btn-search {
    top: 12px;
    left: 16px;
    font-size: 16px;
}

._blackboard {
    padding-top: 30px;
}

.half-block {
    padding: 23px 22px;
    margin-bottom: 22px;
}

/*.block .sm-title, .block .xs-title{
    padding-right: 30px;
}*/
.block-table {
    font-size: 12px;
    line-height: 1.33;
    color: #1d273e;
    /*border-radius: 4px;*/
    -webkit-box-shadow: 0 7px 14px 0 rgba(204, 207, 221, 0.3);
    -moz-box-shadow: 0 7px 14px 0 rgba(204, 207, 221, 0.3);
    box-shadow: 0 7px 14px 0 rgba(204, 207, 221, 0.3);
    background-color: #fff;
    margin-bottom: 22px;
}

.block-table:after {
    display: table;
    clear: both;
    content: '';
}

.block-table-inner {
    /*width: 50%;*/
    float: left;
    padding: 12px;
    border-top: solid 1px #d9dce2;
    position: relative;
    /*padding-right: 50px;*/
}

.block-table .block-table-inner:nth-child(-n+2) {
    border-top: 0;
}

.block-table .block-table-inner:nth-child(even) {
    border-left: solid 1px #d9dce2;
}

.half-table tr td {
    width: 50%;
    border: 0;
    padding: 12px;
    border-top: solid 1px #d9dce2;
}

.half-table tr:first-child td {
    border-top: 0;
}

.half-table tr td:nth-child(2) {
    border-left: solid 1px #d9dce2;
}

.indicator-table .os-font {
    font-size: 17px;
    line-height: 1;
    color: #9599a7;
    position: absolute;
    top: 10px;
    right: 12px;
    z-index: 1
}

.reform-list .os-font {
    z-index: auto;
}

.indicator-table tr td {
    padding-right: 30px;
    position: relative;
    vertical-align: top;
}

.btn-add {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    -webkit-box-shadow: 0 7px 14px 0 rgba(204, 207, 221, 0.3);
    -moz-box-shadow: 0 7px 14px 0 rgba(204, 207, 221, 0.3);
    box-shadow: 0 7px 14px 0 rgba(204, 207, 221, 0.3);
    background-color: #357fed;
    color: #fff !important;
    position: fixed;
    bottom: 30px;
    right: 30px;
    z-index: 5;
    border: 1px solid #357fed;
    transition: all 0.3s;
    overflow: hidden;
}

.btn-add:after {
    background-color: #205fe4;
    content: "";
    position: absolute;
    z-index: -1;
    top: 50%;
    left: 50%;
    border-radius: 15px;
    -webkit-transition: .3s all ease-out;
    -moz-transition: .3s all ease-out;
    -ms-transition: .3s all ease-out;
    -o-transition: .3s all ease-out;
    transition: .3s all ease-out;
    height: 0;
    width: 0;
}

.btn-add:before {
    content: '\e837';
    font-family: "Icons";
}

.btn-add:hover {
    color: #fff !important;
}

.btn-add:focus:after, .btn-add:active:after, .btn-add:hover:after {
    width: 104%;
    left: -2%;
    height: 104%;
    top: -2%;
}

.btn-comments {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    -webkit-box-shadow: 0 7px 14px 0 rgba(204, 207, 221, 0.3);
    -moz-box-shadow: 0 7px 14px 0 rgba(204, 207, 221, 0.3);
    box-shadow: 0 7px 14px 0 rgba(204, 207, 221, 0.3);
    background-color: #fff;
    color: #357fed;
    position: fixed;
    bottom: 40px;
    left: 40px;
    z-index: 5;
    border: 1px solid #357fed;
    transition: all 0.3s;
    opacity: 0.6;
    overflow: hidden;
}

.btn-comments:hover {
    opacity: 1;
    transition: all 0.3s;
}


.btn-comments:after {
    background-color: #205fe4;
    content: "";
    position: absolute;
    z-index: -1;
    top: 50%;
    left: 50%;
    border-radius: 50%;
    -webkit-transition: .3s all ease-out;
    -moz-transition: .3s all ease-out;
    -ms-transition: .3s all ease-out;
    -o-transition: .3s all ease-out;
    transition: .3s all ease-out;
    height: 0;
    width: 0;
}

.btn-comments:before {
    content: '\e83e';
    font-size: 16px;
    font-family: "Icons";
}

.btn-comments:focus:before, .btn-comments:active:before, .btn-comments:hover:before {
    color: #fff;
}

.btn-comments:focus:after, .btn-comments:active:after, .btn-comments:hover:after {
    width: 104%;
    left: -2%;
    height: 104%;
    top: -2%;
}

/*.btn-add:hover {
    background-color: #f2f7ff;
    color: #357fed;
}*/
.search-form + .sort-block {
    margin-top: -15px;
}

.sort-block {
    /*margin-bottom: 15px;*/
    position: relative;
}

.sort-block:after {
    display: table;
    clear: both;
    content: '';
}

.sort-left {
    float: left;
    margin-right: 20px;
}

.sort-right {
    float: right;
}

.sort-label {
    cursor: pointer;
    white-space: nowrap;
}

.sort-label:not(.open):hover .sort-text {
    text-decoration: underline;
}

.sort-label + .sort-label {
    margin-left: 20px;
}

.sort-label .btn-carret {
    width: auto;
    height: auto;
    font-size: 12px;
    font-weight: normal;
    margin-left: 5px;
}

.success-color .btn-carret {
    color: #2be56d;
}

.open .btn-carret:before {
    content: '\e802';
}

.two-row > div:nth-child(2n+1) {
    clear: both;
}

.color-info {
    color: #357fed !important;
}

.sort-label:not(.open) .btn-carret:hover {
    color: #1d273e;
}

.list-method-view .block-table-inner {
    /*width: 25%;*/
    border-top: 0;
    border-left: solid 1px #d9dce2;
    padding-top: 42px;
    padding-bottom: 28px;
    display: table-cell;
    vertical-align: top;
    white-space: normal;
    text-overflow: inherit;
    /*padding-right: 12px;*/
    float: none;
    height: 100%;
}

.impact-assessments-block > .list-method-view .block-table-inner {
    height: auto;
}

.block:after {
    display: table;
    clear: both;
    content: '';
}

.info-request .auto-number {
    font-size: 14px;
    color: #909090;
    font-variant: full-width;
    font-weight: bold;
    margin-right: 5px;
}

.list-method-view .block-table {
    max-width: 69%;
    display: inline-block;
    vertical-align: top;
    margin: 15px;
    float: right;
    margin-right: 45px;
}

.list-method-view .block .sm-title, .list-method-view .block .xs-title {
    width: 30%;
    max-width: 580px;
    display: inline-block;
    vertical-align: top;
    background-color: #fff;
    padding: 20px;
}

.list-method-view .block .sm-title, .list-method-view .block .xs-title h4 {
    margin-top: 0;
    margin-bottom: 0;
    font-size: 14px;
}

.list-method-view .block .sm-title, .list-method-view .block .xs-title h4 i {
    color: #357fed;
    margin-right: 5px;
}

.list-method-view .block-table-inner:first-child {
    border-left: 0
}

/*.list-method-view .block .btn{*/
/*position: absolute;*/
/*bottom: 12px;*/
/*right: 8px;*/
/*z-index: 2;*/
/*width: 30px;*/
/*padding: 9px;*/
/*font-size: 0;*/
/*background: #fff;*/
/*border-radius: 50%;*/
/*}*/
/*.list-method-view .block .btn i {*/
/*margin-right: 0;*/
/*font-size: 10px;*/
/*display: block;*/
/*}*/
/*.list-method-view .block{
    padding-right: 75px;
}*/
.list-method-view .indicator-table .os-font {
    right: auto;
    left: 12px;
    font-size: 14px;
    display: block;
    padding-right: 5px;
    line-height: 1.2;
}

.doc-status-nav {
    border-radius: 4px;
    border: solid 1px #d9dce2;
    padding: 4px 0;
    margin-bottom: 17px;
}

.doc-status-nav:after {
    display: table;
    clear: both;
    content: '';
}

.doc-status-nav li {
    list-style: none;
    float: left;
    width: 14.28%;
    padding: 0 4px;
}

.doc-status-nav .btn {
    color: #1d273e;
    background: transparent;
    display: block;
    text-decoration: underline;
}

.title-doc-status {
    position: relative;
}

.title-doc-status:before {
    width: 10px;
    height: 100%;
    border-radius: 4px 0 0 4px;
    content: '';
    position: absolute;
    top: 0;
    left: -1px;
    z-index: 1;
    -webkit-transition: .3s all;
    -moz-transition: .3s all;
    -ms-transition: .3s all;
    -o-transition: .3s all;
    transition: .3s all;
}

.block:hover:before {
    /*opacity: 0;*/
}

.doc-status-nav .btn:before {
    width: 10px;
    height: 10px;
    border-radius: 1px;
    content: '';
    display: inline-block;
    position: relative;
    top: 1px;
    margin-right: 9px;
}

.doc-status-nav .btn.active, .doc-status-nav .btn:hover {
    color: #357fed;
    background-color: #fff;
    border-color: #d9dce2;
    text-decoration: none;
}

.page-visible-btn .btn span {
    text-decoration: underline;
}

.white-control::-webkit-input-placeholder {
    color: #9599a7;
}

.white-control:-moz-placeholder {
    color: #9599a7;
}

.white-control:-moz-placeholder {
    color: #9599a7;
}

.white-control:-ms-input-placeholder {
    color: #9599a7;
}

img {
    max-width: 100%;
    height: auto;
}

.mob-menu-btn {
    color: #2d374c;
    border: 0;
    background: transparent;
    font-size: 18px;
    font-weight: normal;
    -webkit-transition: .3s all;
    -moz-transition: .3s all;
    -ms-transition: .3s all;
    -o-transition: .3s all;
    transition: .3s all;
    background-color: transparent;
    border-radius: 50%;
    -webkit-transform: rotate(90deg);
    -moz-transform: rotate(90deg);
    -ms-transform: rotate(90deg);
    -o-transform: rotate(90deg);
    transform: rotate(90deg);
    display: none;
    position: relative;
    top: -4px;
    margin-left: 5px;
}

.mob-menu-btn:before {
    font-family: 'Icons';
    content: '\e800';
}

.problem-block-inner {
    position: relative;
}

.problem-block:after {
    display: table;
    content: '';
    clear: both;
}

.problem-block {
    border-radius: 4px;
    border: solid 1px #d9dce2;
    background-color: #fff;
    margin-bottom: 22px;
}

.problem-block-inner {
    float: left;
    width: 33.333333333%;
    border-left: solid 1px #d9dce2;
    padding: 27px 22px 27px 75px;
    position: relative;
}

.problem-block-inner:first-child {
    border-left: 0;
}

.problem-block-icon {
    width: 34px;
    height: 34px;
    background-color: #1d273e;
    position: absolute;
    top: 20px;
    left: 22px;
    z-index: 1;
    text-align: center;
    line-height: 32px;
    border-radius: 50%;
}

.problem-block-inner .xs-title {
    margin: 0 0 2px;
}

.problem-block-inner .os-font {
    font-size: 13px;
    font-weight: bold;
    line-height: 1.31;
    color: #2be56d;
    margin-bottom: 17px;
}

.table-type {
    margin: 0;
}

.table-type th {
    font-size: 10px;
    font-weight: 600;
    line-height: 1.9;
    color: #8c93a6;
    text-transform: uppercase;
}

.table-type td, .table-type th {
    border: 0;
}

.table-type td:first-child, .table-type th:first-child {
    padding-left: 0;
}

.table-type td:last-child, .table-type th:last-child {
    padding-right: 0;
}

.table-type a {
    color: #1d273e;
    text-decoration: underline;
}

.table-type a:hover {
    color: #357fed;
}

.block-type {
    border-radius: 4px;
    border: solid 1px #d9dce2;
    background-color: #fff;
    margin-bottom: 22px;
    position: relative;
}

.block-type-head {
    padding: 25px 60px 25px 22px;
    border-bottom: solid 1px #d9dce2;
    position: relative;
}

.block-type-head + .block-type-inner, .block-type-icon-head + .block-type-inner {
    margin-top: -1px;
}

.block-type-inner {
    border-top: solid 1px #d9dce2;
    padding: 15px 22px 18px;
    position: relative;
}

.block-type-inner a {
    color: #1d273e;
    text-decoration: underline;
}

.block-type-inner a:hover {
    color: #357fed;
}

.block-type-head .os-font {
    font-size: 13px;
    font-weight: bold;
    line-height: 1.62;
    color: #357fed;
}

.block-type-head .sm-title, .block-type-head .xs-title {
    margin: 0;
}

.circle-img {
    width: 50px;
    height: 50px;
    background-color: #357fed;
    border-radius: 50%;
    color: #fff;
    text-align: center;
    line-height: 50px;
}

.block-type-icon-head {
    padding: 26px 60px 26px 92px;
    border-bottom: solid 1px #d9dce2;
}

.block-type-icon-head .info-label {
    margin: 0;
}

.block-type-icon-head .circle-img {
    position: absolute;
    top: 25px;
    left: 22px;
    z-index: 1;
}

.count-info {
    font-size: 28px;
    font-weight: normal;
    line-height: 1.29;
    color: #1d273e;
    white-space: nowrap;
}

.block-mark {
    padding-right: 200px;
}

.block-mark .mark-info {
    text-align: right;
    position: absolute;
    top: 16px;
    right: 30px;
    width: 150px;
    z-index: 1;
}

.block-mark .os-font {
    font-size: 13px;
    font-weight: normal;
    line-height: 1.62;
    color: #1d273e;
}

.trash-btn-block {
    padding-right: 50px !important;
}

.trash-btn-block .btn30 {
    position: absolute;
    top: 10px;
    right: 0;
    z-index: 1;
}

.dlt-btn:before {
    font-family: 'Icons';
    content: '\e806';
    font-size: 18px;
}

.btn30 {
    width: 30px;
    height: 30px;
    border-radius: 4px;
    display: inline-block;
    text-align: center;
    line-height: 28px;
    text-decoration: none !important;
    -webkit-transition: .3s all;
    -moz-transition: .3s all;
    -ms-transition: .3s all;
    -o-transition: .3s all;
    transition: .3s all;
    padding: 0 !important;
}

a, .btn, [class^="btn-"] {
    -webkit-transition: .3s all;
    -moz-transition: .3s all;
    -ms-transition: .3s all;
    -o-transition: .3s all;
    transition: .3s all;
}

.btn30:hover {
    color: #357fed;
    background-color: #f2f7ff;
    border-color: #357fed;
}

.btn30-blue {
    background-color: #f2f7ff;
    color: #357fed;
    border-color: #f2f7ff;
}

.btn30-blue:hover {
    background-color: #d4e5ff;
    border-color: #f2f7ff;
    color: #357fed;
}

.btn30-blue:active {
    background-color: #357fed;
    border-color: #357fed;
    color: #fff;
}

.btn-prev:before {
    font-family: "Icons";
    content: '\e838';
    -webkit-transform: rotate(180deg);
    -moz-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    -o-transform: rotate(180deg);
    transform: rotate(180deg);
    font-size: 12px;
    display: inline-block;
}

.page-head .btn30 {
    float: left;
    margin-right: 25px;
    position: relative;
    top: 1px;
    margin-bottom: 25px;
}

.block-mark .xs-title {
    margin: 0;
}

.color-grey {
    color: #9599a7;
}

.block-type-inner:first-child {
    border-top: 0;
}

.btn-tags {
    padding: 6px 12px;
    color: #357fed;
    text-decoration: underline;
    display: inline-block;
    border-radius: 4px;
    background-color: #f2f7ff;
    margin-bottom: 9px;
    margin-right: 5px;
    line-height: 1.45;
    border: 1px solid #f2f7ff;
}

.btn-tags:hover {
    text-decoration: none;
    border-color: #357fed;
}

.link-list-block {
    padding: 18px 28px;
}

.link-list-block .info-label {
    margin: 0 0 17px;
}

.link-list-block p {
    margin-bottom: 15px;
}

.info-label .os-font {
    font-size: 11px;
    font-weight: bold;
    color: #357fed;
}

.nav-menu-head-img {
    margin-bottom: 29px;
}

.nav-menu-head .info-label + div {
    margin-top: 17px;
}

.scroll-all-nav {
    max-height: calc(100vh - 64px);
    max-height: -webkit-calc(100vh - 64px);
    max-height: -o-calc(100vh - 64px);
    overflow-y: auto;
    padding-bottom: 0;
}

.icon-title .xs-title {
    position: relative;
}

.btn-star {
    font-size: 14px;
    color: #1d273e;
    border: 0;
    background: transparent;
}

.btn-star:before {
    font-family: "Icons";
    font-weight: normal;
    content: '\e842';
}

.icon-title .btn-star {
    position: absolute;
    top: 21px;
    left: 22px;
    z-index: 1;
}

.check-star .btn-star:before, .check-star.btn-star:before {
    content: '\e843';
    color: #f4d949;
}

.sub-block-wrap-img {
    padding: 20px 22px 20px 94px;
    position: relative;
}

.sub-block-wrap-img .circle-img {
    position: absolute;
    top: 28px;
    left: 22px;
    z-index: 1;
}

.progress-text {
    margin-top: 32px;
    overflow: visible;
    position: relative;
}

.progress-text .sr-only {
    top: -25px;
    overflow: visible;
    clip: auto;
}

/*select*/
select.bs-select-hidden,
select.selectpicker {
    display: none !important;
}

.bootstrap-select {
    width: 220px \0;
    position: relative;
}

.bootstrap-select > .dropdown-toggle {
    width: 100%;
    padding-right: 40px;
    z-index: 1;
}

.bootstrap-select > .dropdown-toggle.bs-placeholder,
.bootstrap-select > .dropdown-toggle.bs-placeholder:hover,
.bootstrap-select > .dropdown-toggle.bs-placeholder:focus,
.bootstrap-select > .dropdown-toggle.bs-placeholder:active {

    text-transform: inherit;
}

.bootstrap-select > select {
    position: absolute !important;
    bottom: 0;
    left: 50%;
    display: block !important;
    width: 0.5px !important;
    height: 100% !important;
    padding: 0 !important;
    opacity: 0 !important;
    border: none;
}

.bootstrap-select > select.mobile-device {
    top: 0;
    left: 0;
    display: block !important;
    width: 100% !important;
    z-index: 2;
}

.has-error .bootstrap-select .dropdown-toggle,
.error .bootstrap-select .dropdown-toggle {
    border-color: #b94a48;
}

.bootstrap-select.fit-width {
    width: auto !important;
}

.bootstrap-select:not([class*="col-"]):not([class*="form-control"]):not(.input-group-btn) {
    width: 216px;
}

.bootstrap-select.form-control {
    margin-bottom: 0;
    padding: 0;
    border: none;
}

.bootstrap-select.form-control:not([class*="col-"]) {
    width: 100%;
}

.bootstrap-select.form-control.input-group-btn {
    z-index: auto;
}

.bootstrap-select.form-control.input-group-btn:not(:first-child):not(:last-child) > .btn {
    border-radius: 0;
}

.bootstrap-select.btn-group:not(.input-group-btn),
.bootstrap-select.btn-group[class*="col-"] {
    float: none;
    display: inline-block;
    margin-left: 0;
}

.bootstrap-select.btn-group.dropdown-menu-right,
.bootstrap-select.btn-group[class*="col-"].dropdown-menu-right,
.row .bootstrap-select.btn-group[class*="col-"].dropdown-menu-right {
    float: right;
}

.form-inline .bootstrap-select.btn-group,
.form-horizontal .bootstrap-select.btn-group,
.form-group .bootstrap-select.btn-group {
    margin-bottom: 0;
}

.form-group-lg .bootstrap-select.btn-group.form-control,
.form-group-sm .bootstrap-select.btn-group.form-control {
    padding: 0;
}

.form-group-lg .bootstrap-select.btn-group.form-control .dropdown-toggle,
.form-group-sm .bootstrap-select.btn-group.form-control .dropdown-toggle {
    height: 100%;
    font-size: inherit;
    line-height: inherit;
    border-radius: inherit;
}

.form-inline .bootstrap-select.btn-group .form-control {
    width: 100%;
}

.bootstrap-select.btn-group.disabled,
.bootstrap-select.btn-group > .disabled {
    cursor: not-allowed;
}

.bootstrap-select.btn-group.disabled:focus,
.bootstrap-select.btn-group > .disabled:focus {
    outline: none !important;
}

.bootstrap-select.btn-group.bs-container {
    position: absolute;
    height: 0 !important;
    padding: 0 !important;
}

.bootstrap-select.btn-group.bs-container .dropdown-menu {
    z-index: 1060;
}

.bootstrap-select.btn-group .dropdown-toggle .filter-option {
    display: inline-block;
    overflow: hidden;
    width: 100%;
    text-align: left;
}

.bootstrap-select.btn-group .dropdown-toggle .caret {
    position: absolute;
    top: 50%;
    right: 27px;
    margin-top: -2px;
    vertical-align: middle;
}

.bootstrap-select.btn-group[class*="col-"] .dropdown-toggle {
    width: 100%;
}

.bootstrap-select.btn-group .dropdown-menu {
    min-width: 100%;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    margin-top: 10px;
    padding-top: 14px;
}

.bootstrap-select.btn-group .dropdown-menu.inner {
    position: static;
    float: none;
    border: 0;
    padding: 0;
    margin: 0;
    border-radius: 0;
    -webkit-box-shadow: none;
    box-shadow: none;
}

.dropdown-menu > li > a:focus, .dropdown-menu > li > a:hover {
    color: #357fed;
    background-color: transparent;
}

.bootstrap-select.btn-group .dropdown-menu li {
    position: relative;
    margin-bottom: 12px;
}

.bootstrap-select.btn-group .dropdown-menu li.active small {
    color: #fff;
}

.bootstrap-select.btn-group .dropdown-menu li.disabled a {
    cursor: not-allowed;
}

.bootstrap-select.btn-group .dropdown-menu li a {
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    white-space: normal;
    display: inline-block;
    padding: 0 15px;
}

.bootstrap-select .btn {
    text-transform: inherit;
    font-weight: 400;
}

.bs-placeholder {
    opacity: 0.5;
}

.bootstrap-select.btn-group .dropdown-menu li a.opt {
    position: relative;
    padding-left: 2.25em;
}

.bootstrap-select.btn-group .dropdown-menu li a span.check-mark {
    display: none;
}

.bootstrap-select.btn-group .dropdown-menu li a span.text {
    display: inline-block;
    text-decoration: underline;
}

.bootstrap-select.btn-group .dropdown-menu li small {
    padding-left: 0.5em;
}

.bootstrap-select.btn-group .dropdown-menu .notify {
    position: absolute;
    bottom: 5px;
    width: 96%;
    margin: 0 2%;
    min-height: 26px;
    padding: 3px 5px;
    background: #f5f5f5;
    border: 1px solid #e3e3e3;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.05);
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.05);
    pointer-events: none;
    opacity: 0.9;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}

.bootstrap-select.btn-group .no-results {
    padding: 3px;
    background: #f5f5f5;
    margin: 0 5px;
    white-space: nowrap;
}

.bootstrap-select.btn-group.fit-width .dropdown-toggle .filter-option {
    position: static;
}

.bootstrap-select.btn-group.fit-width .dropdown-toggle .caret {
    position: static;
    top: auto;
    margin-top: -1px;
}

.bootstrap-select.btn-group.show-tick .dropdown-menu li.selected a span.check-mark {
    position: absolute;
    display: inline-block;
    right: 15px;
    margin-top: 5px;
}

.bootstrap-select.btn-group.show-tick .dropdown-menu li a span.text {
    margin-right: 34px;
}

.bootstrap-select.show-menu-arrow.open > .dropdown-toggle {
    z-index: 1061;
}

.bootstrap-select.show-menu-arrow .dropdown-toggle:before {
    content: '';
    border-left: 7px solid transparent;
    border-right: 7px solid transparent;
    border-bottom: 7px solid rgba(204, 204, 204, 0.2);
    position: absolute;
    bottom: -4px;
    left: 9px;
    display: none;
}

.bootstrap-select.show-menu-arrow .dropdown-toggle:after {
    content: '';
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-bottom: 6px solid white;
    position: absolute;
    bottom: -4px;
    left: 10px;
    display: none;
}

.bootstrap-select.show-menu-arrow.dropup .dropdown-toggle:before {
    bottom: auto;
    top: -3px;
    border-top: 7px solid rgba(204, 204, 204, 0.2);
    border-bottom: 0;
}

.bootstrap-select.show-menu-arrow.dropup .dropdown-toggle:after {
    bottom: auto;
    top: -3px;
    border-top: 6px solid white;
    border-bottom: 0;
}

.bootstrap-select.show-menu-arrow.pull-right .dropdown-toggle:before {
    right: 12px;
    left: auto;
}

.bootstrap-select.show-menu-arrow.pull-right .dropdown-toggle:after {
    right: 13px;
    left: auto;
}

.bootstrap-select.show-menu-arrow.open > .dropdown-toggle:before,
.bootstrap-select.show-menu-arrow.open > .dropdown-toggle:after {
    display: block;
}

.bs-searchbox,
.bs-actionsbox,
.bs-donebutton {
    padding: 4px 8px;
}

.bs-actionsbox {
    width: 100%;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}

.bs-actionsbox .btn-group button {
    width: 50%;
}

.bs-donebutton {
    float: left;
    width: 100%;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}

.bs-donebutton .btn-group button {
    width: 100%;
}

.bs-searchbox + .bs-actionsbox {
    padding: 0 8px 4px;
}

.bs-searchbox .form-control {
    margin-bottom: 0;
    width: 100%;
    float: none;
}

.bootstrap-select .bs-caret {
    display: none;
}

.btn-group.open .dropdown-toggle {
    -webkit-box-shadow: none;
    box-shadow: none;
}

.bootstrap-select:before {
    pointer-events: none;
    position: absolute;
    width: 30px;
    height: 30px;
    border-radius: 4px;
    background-color: #f2f7ff;
    top: 50%;
    margin-top: -15px;
    z-index: 10;
    right: 6px;
    color: #357fed;
    border: 0;
    -webkit-transition: .3s all ease-in-out;
    -moz-transition: .3s all ease-in-out;
    -ms-transition: .3s all ease-in-out;
    -o-transition: .3s all ease-in-out;
    transition: .3s all ease-in-out;
    font-family: 'Icons';
    content: '\e82e';
    font-size: 6px;
    text-align: center;
    line-height: 30px;
}

.bootstrap-select:hover:before {
    background-color: #d4e5ff;
}

.bootstrap-select.open:before {
    content: '\e82f';
    background-color: #357fed;
    color: #fff;
}

.block .colap-up {
    position: absolute;
    bottom: 22px;
    right: 22px;
    z-index: 1;
}


@media screen and  (max-height: 680px) and (min-width: 1219px) {
    .auth-block-inner {
        padding-top: 0;
    }
}

@media (max-width: 1280px) {
    .circle-progress-wrap {
        padding: 10px 0 0;
    }

    .circle-progress, .circle-progress svg {
        width: 120px;
        height: 120px;
    }

    .circle-progress-block {
        width: 90px;
        height: 90px;
        top: 20px;
        left: 20px;
        padding: 30px 10px 0;
    }
}

@media (max-width: 1199px) {
    /*.header {
        padding: 17px 0 0;
    }
    .page-section {
        padding: 63px 40px 0 0;
    }
    .btn-menu {
        right: 10px;
    }*/
    .opened_menu .page-section {
        /*padding-right: 10px;*/
    }

    .list-method-view .block .sm-title {
        width: 100%;
        display: block;
    }

    /*.list-method-view .block-table{
        float: none;
        display: inline-block;
        max-width: 100%;
        margin-bottom: 22px;
    }*/
    .breadcrumb {
        margin-bottom: 10px;
        margin-top: 30px;
    }

    /*.list-method-view .block .sm-title, .list-method-view .block .xs-title {
        width: 96%;
        display: block;
    }
    .list-method-view .block {
        padding-right: 22px;
    }
    .list-method-view .block .btn {
        position: relative;
        bottom: 0;
        right: 0;
        width: 100%;
        font-size: 10px;
    }*/
    .list-method-view .block .btn i {
        margin-right: 5px;
        display: inline-block;
    }

    .problem-block-inner {
        padding: 65px 22px 27px 22px;
    }

    .empty-block {
        padding: 30px;
    }

    .icon-title .btn-star {
        top: 22px;
    }


}

@media (max-width: 991px) {
    .icon-title .btn-star {
        top: 21px;
    }

    /*.main-menu li {*/
    /*    margin-right: 10px;*/
    /*}*/
    .opened_menu .two-row .col-sm-6 {
        width: 100%;
    }

    .sort-label + .sort-label {
        margin-left: 0;
    }

    .sort-label {
        margin-right: 10px;
    }

    .sort-label:last-child {
        margin-right: 0;
    }

    .sort-label .btn-carret {
        margin-left: 2px;
    }

    .opened_menu .page-head {
        padding-right: 0;
    }

    .opened_menu .page-visible-btn {
        position: relative;
        top: -15px;
    }

    /*.block, .half-block {
        padding: 22px 15px;
    }
    .list-method-view .block {
        padding-right: 15px;
    }*/
    .opened_menu .doc-status-nav li {
        float: none;
        width: 100%;
        padding: 2px 4px;
    }

    .opened_menu .doc-status-nav .btn {
        text-align: left;
    }

    .opened_menu ._blackboard .col-sm-6 {
        width: 100%;
    }

    .opened_menu .problem-block-inner {
        float: none;
        width: 100%;
        border-bottom: solid 1px #d9dce2;
        border-left: 0;
    }

    .opened_menu .problem-block-inner:last-child {
        border-bottom: 0;
    }

    .block-type-head {
        padding-left: 15px;
    }

    .block-type-inner {
        padding-left: 15px;
        padding-right: 15px;
    }

    .block-type-icon-head .circle-img {
        left: 15px;
    }

    .block-type-icon-head {
        padding-left: 85px;
    }

    .circle-progress svg path {
        stroke-width: 4;
    }

    .circle-progress, .circle-progress svg {
        width: 96px;
        height: 96px;
    }

    .circle-progress-block {
        width: 72px;
        height: 72px;
        top: 12px;
        left: 12px;
        padding: 21px 10px 0;
        font-size: 8px;
    }

    .circle-progress-count {
        font-size: 14px;
    }

    .sub-block-wrap {
        padding: 0 15px;
    }
}

@media (max-width: 767px) {
    .problem-block-inner {
        float: none;
        width: 100%;
        border-bottom: solid 1px #d9dce2;
        border-left: 0;
    }

    .reform-widget-wrapper .progress-widget .progress {
        height: 2px;
    }

    .reform-widget-wrapper .progress-widget {
        top: inherit;
        bottom: 2px;
    }

    .table-responsive {
        margin-bottom: 0;
        border: 0;
    }

    .problem-block-inner:last-child {
        border-bottom: 0;
    }

    .metod-briefly {
        max-height: 100%;
        overflow: visible;
        padding: 20px 15px;
    }

    .nav-menu-foot {
        position: relative;
        bottom: 0;
        left: 0;
        right: 0;
        padding: 0 28px 28px;
    }

    ._blackboard {
        padding-top: 0;
    }

    .page-visible-btn .btn {
        font-size: 8px;
    }

    .page-visible-btn {
        margin-bottom: 5px;
        /*display: none;*/
    }

    .btn-add {
        width: 40px;
        height: 40px;
        bottom: 15px;
        right: 15px;
    }

    .auth-sidebar {
        padding: 15px;
        width: 300px;
    }

    .auth-head {
        margin-bottom: 15px;
    }

    .auth-bg {
        width: calc(100% - 300px);
    }

    .auth-block .section-title {
        margin: 0 0 10px;
        font-size: 20px;
    }

    .auth-block-inner {
        padding-top: 0;
    }

    .form-group {
        margin-bottom: 15px;
    }

    .auth-body .form-control {
        height: 40px;
    }

    .auth-body .btn-circle {
        padding: 12px 18px;
        width: 100%;
    }

    .form-group-lg {
        margin-top: 20px;
    }

    .list-method-view .block-table {
        display: block;
    }

    /*.main-menu {*/
    /*    list-style: none;*/
    /*    margin: 4px 0 0;*/
    /*    !*display: none;*!*/
    /*    position: absolute;*/
    /*    top: 30px;*/
    /*    background: #fff;*/
    /*    width: 238px;*/
    /*    -webkit-box-shadow: 0 7px 14px 0 rgba(204, 207, 221, 0.3);*/
    /*    -moz-box-shadow: 0 7px 14px 0 rgba(204, 207, 221, 0.3);*/
    /*    box-shadow: 0 7px 14px 0 rgba(204, 207, 221, 0.3);*/
    /*    right: 0;*/
    /*}*/
    /*.main-menu li {*/
    /*    margin-right: 0;*/
    /*    display: block;*/
    /*}*/
    /*.main-menu li:last-child a{*/
    /*    border-bottom: 0;*/
    /*}*/
    /*.main-menu li a {*/
    /*    padding-bottom: 0;*/
    /*    border-bottom: solid 1px #d9dce2;*/
    /*    display: block;*/
    /*    padding: 10px 15px;*/
    /*}*/
    /*.main-menu li.current-menu-item a, .main-menu li a:hover {*/
    /*    border-color: #d9dce2;*/
    /*}*/
    .mob-menu-btn {
        display: inline-block;
    }

    .logo_block {
        width: 50px;
        position: relative;
        top: -1px;
    }

    .user-nav-ava, .user-nav-ava img {
        width: 20px;
        height: 20px;
        border-radius: 2px;
    }

    .user-nav-ava, .user-nav-ava img {
        width: 20px;
        height: 20px;
        border-radius: 2px;
    }

    .user-nav-ava {
        margin-right: 5px;
    }

    .user-nav .btn-carret {
        margin-top: 0;
        position: relative;
        top: -2px;
    }

    .btn-carret {
        border-left: 4px solid transparent;
        border-right: 4px solid transparent;
        /*border-top: 4px solid #1d273e;*/
    }

    .header .search-form {
        margin-right: 5px;
    }

    .search-form .form-control {
        height: 22px;
        width: 160px;
        font-size: 8px;
    }

    .search-form .btn-search {
        top: 3px;
        left: 8px;
        font-size: 11px;
    }

    .header {
        padding: 10px 0;
        height: 44px;
    }

    .page-section {
        padding: 60px 0 20px;
    }

    .breadcrumb {
        margin-bottom: 5px;
        margin-top: 0;
        padding-right: 35px;
    }

    .section-title, h1 {
        font-size: 10px;
    }

    .btn-menu {
        right: 4px;
        top: 54px;
        position: absolute;
        z-index: 10;
    }

    .page-head {
        padding-right: 35px;
    }

    .white-control {
        height: 38px;
    }

    .search-form.lg-search .btn-search {
        top: 9px;
        font-size: 14px;
    }

    .doc-status-nav li {
        float: none;
        width: 100%;
        padding: 2px 4px;
    }

    .doc-status-nav .btn {
        text-align: left;
    }

    .opened_menu .page-section {
        width: 100%;
        padding-right: 0;
    }

    .nav-menu {
        width: 100%;
        border-left: 0;
        position: relative;
        top: 0;
        bottom: 0;
        left: 0;
        z-index: 10;
        padding: 0;
        opacity: 1;
        background: #fff;
        margin-top: 44px;
        display: none;
    }

    .opened_menu .nav-menu {
        display: block;
    }

    .opened_menu .btn-menu:before {
        font-size: 14px;
        -webkit-transform: rotate(-90deg);
        -moz-transform: rotate(-90deg);
        -ms-transform: rotate(-90deg);
        -o-transform: rotate(-90deg);
        transform: rotate(-90deg);
        display: inline-block;
    }

    .opened_menu .btn-menu {
        left: 4px;
        top: 10px;
    }

    nav-menu-inner {
        max-height: 100% !important;
    }

    .list-method-view .block-table-inner, .block-table-inner {
        width: 100% !important;
        display: block;
        border-left: 0 !important;
        border-top: solid 1px #d9dce2 !important;
        padding-top: 12px;
        padding-bottom: 12px;
        padding-right: 50px;
    }

    .list-method-view .block-table-inner:first-child, .block-table-inner:first-child {
        border-top: 0 !important;
    }

    .list-method-view .indicator-table .os-font {
        right: 12px;
        left: auto;
    }

    .nav-menu-inner {
        max-height: 100% !important;
        overflow: auto !important;
    }

    .sort-right {
        float: none;
    }

    btn-nav-wrap {
        top: 2px;
        right: 10px;
    }

    .block-type-head {
        padding-left: 15px;
    }

    .block-type-inner {
        padding-left: 15px;
        padding-right: 15px;
    }

    .block-mark .mark-info {
        text-align: left;
        position: relative;
        top: 0;
        right: 0;
        width: 100%;
        margin-top: 5px;
    }

    .block-type-icon-head .circle-img {
        left: 15px;
        top: 24px;
    }

    .block-type-icon-head {
        padding-left: 78px;
    }

    .count-info {
        font-size: 22px;
    }

    .circle-img {
        width: 44px;
        height: 44px;
        line-height: 44px;
    }

    .nav-menu-head {
        padding: 22px 50px 13px 15px;
    }

    .method-list .method-list-block:before {
        left: 15px;
    }

    .method-list-block a {
        padding: 18px 15px 18px 50px;
    }

    .page-head .btn30 {
        margin-right: 15px;
        top: -2px;
        margin-bottom: 20px;
    }

    .scroll-all-nav {
        max-height: fit-content;
    }

    .empty-block > div {
        display: block;
        text-align: center;
    }

    .empty-block > div:last-child {
        text-align: center;
        margin-top: 20px;
    }

    .empty-block > div:last-child .btn {
        width: auto;
        display: inline-block;
        min-width: 250px;
    }

    .circle-progress, .circle-progress svg {
        width: 144px;
        height: 144px;
    }

    .circle-progress-block {
        width: 104px;
        height: 104px;
        top: 20px;
        left: 20px;
        padding: 30px 10px 0;
        font-size: 12px;
    }

    .circle-progress-count {
        font-size: 20px;
    }

    .circle-progress svg path {
        stroke-width: 7;
    }

    .page-head-left {
        float: none;
    }

    .page-head-right {
        float: none;
    }


}

@media (orientation: landscape) and (max-width: 767px) {
    .auth-footer {
        position: relative;
        bottom: auto;
        left: 0;
        right: 0;
        margin-top: 30px;
        margin-bottom: 20px;
    }

    .auth-sidebar {
        position: relative;
    }

    .global-search-results {
        width: 94.4% !important;
        margin: 10px;
    }

    .close-global-search {
        position: absolute;
        right: 8px;
        top: 10px;
        color: #ffffff !important;
        cursor: pointer;
        font-size: 17px;
        background-color: #4d8cef;
        width: 25px;
        height: 25px;
        padding: 5px;
        line-height: 0.8;
        text-align: center;
        border-radius: 50%;
    }
}

@media (max-width: 767px) and (min-width: 467px) {
    .col-sx-6 {
        width: 50%;
        float: left;
    }

    .col-sx-7 {
        width: 58.33333333%;
        float: left;
    }

    .col-sx-5 {
        width: 41.66666667%;
        float: left;
    }

    .col-sx-4 {
        width: 33.33333333%;
        float: left;
    }

    .col-sx-3 {
        width: 25%;
        float: left;
    }
}

@media (max-width: 467px) {
    .auth-sidebar {
        width: 100%;
    }

    .auth-head {
        margin-bottom: 25px;
    }

    .empty-block {
        padding: 42px 30px;
    }

    .global-search-results {
        width: 94.4% !important;
        margin: 10px;
    }

    .close-global-search {
        position: absolute;
        right: 8px;
        top: 10px;
        color: #ffffff !important;
        cursor: pointer;
        font-size: 17px;
        background-color: #4d8cef;
        width: 25px;
        height: 25px;
        padding: 5px;
        line-height: 0.8;
        text-align: center;
        border-radius: 50%;
    }
}

.border-block {
    border-radius: 4px;
    border: solid 2px #d9dce2;
}

.border-block-1 {
    border-radius: 5px;
    border: solid 1px #d9dce2;
}

.btn-group .btn.active {
    color: #357fed;
    background-color: #dfe9f9;
    border-radius: 5px !important;
}

.btn-group .btn:hover {
    background-color: #f1f1f1;
    border-radius: 5px !important;
}

.btn-group .btn {
    color: #1d273e;
}

.btn-group-blue .btn.active {
    color: #fff;
    background-color: #357fed;
    border-radius: 5px !important;
    font-weight: 600;
}

.btn-group-blue .btn:hover {
    color: #fff;
    background-color: #357fed;
}

.btn-group-blue .btn {
    color: #1D273E;
    font-weight: 400;
}

.shadow-block {
    -webkit-box-shadow: 0 7px 14px 0 rgba(204, 207, 221, 0.3);
    -moz-box-shadow: 0 7px 14px 0 rgba(204, 207, 221, 0.3);
    box-shadow: 0 7px 14px 0 rgba(204, 207, 221, 0.3);
}

.shadow-block2 {
    -webkit-box-shadow: 0 27px 23px 0 rgba(167, 169, 178, 0.37);
    -moz-box-shadow: 0 27px 23px 0 rgba(167, 169, 178, 0.37);
    box-shadow: 0 27px 23px 0 rgba(167, 169, 178, 0.37);
    cursor: grabbing;
}

.user-nav-menu ul {
    width: 186px;
}

.user-nav-menu ul li {
    word-break: normal;
    white-space: normal;
}

.user-nav-menu li:first-child {
    padding-top: 10px;
    font-weight: 600;
    padding-bottom: 0px;
}

.user-nav-menu li:last-child {
    padding-bottom: 10px;
}

.user-nav-menu li:last-child img {
    margin-right: 8px;
}

.user-nav-menu li:nth-child(2),
.user-nav-menu li.menu-last {
    border-bottom: solid 1px #d9dce2;
    padding-bottom: 15px;
    padding-top: 2px;
}

.user-nav-menu li .user-role {
    border-radius: 2px;
    background-color: #f2f7ff;
    display: inline-block;
    padding: 2px 8px 3px;
    color: #357fed;
    text-transform: uppercase;
    font-size: 10px;
    font-weight: 600;
}

.user-nav-menu li:nth-child(3) {
    padding-top: 15px;
}

.user-nav-menu ul li {
    list-style: none;
    /*white-space: normal;*/
    padding: 5px 18px;
}

.btn-view.disabled {
    opacity: 0.7;
    background-color: #f5f6fa;
}

.view-drop .dropdown-menu {
    min-width: 33px;
    border-radius: 4px;
    -webkit-box-shadow: 0 8px 18px 0 rgba(167, 169, 178, 0.39);
    -moz-box-shadow: 0 8px 18px 0 rgba(167, 169, 178, 0.39);
    box-shadow: 0 8px 18px 0 rgba(167, 169, 178, 0.39);
    background-color: #fff;
    top: auto;
    bottom: 100%;
    margin-bottom: 10px;
    padding: 0;
}

.view-drop .dropdown-menu a {
    color: #357fed;
    display: block;
    height: 30px;
    border-bottom: solid 1px #dedfe5;
    line-height: 30px;
    text-align: center;
    padding: 0;
}

.view-drop .dropdown-menu li:last-child a {
    border: 0;
}

.btn-view {
    height: 38px;
    width: 120px;
    border-radius: 4px;
    -webkit-box-shadow: 0 8px 18px 0 rgba(167, 169, 178, 0.39);
    -moz-box-shadow: 0 8px 18px 0 rgba(167, 169, 178, 0.39);
    box-shadow: 0 8px 18px 0 rgba(167, 169, 178, 0.39);
    background-color: #fff;
    display: none;
    cursor: pointer;
}

.btn-view-left {
    float: left;
    font-family: 'Open Sans', sans-serif;
    font-size: 11px;
    font-weight: 600;
    line-height: 1.91;
    color: #1d273e;
    padding: 7px;
    text-align: center;
}

.btn-view-left .icon-angle-down {
    font-size: 14px;
    line-height: 1;
    display: inline-block;
    margin-left: 5px;
    position: relative;
    top: 1px;
}

.btn-view-left .icon-angle-down:before {
    font-weight: 700;
}

.btn-view-icon {
    width: 24px;
    height: 24px;
    border-radius: 4px;
    background-color: #357fed;
    display: inline-block;
    vertical-align: middle;
    text-align: center;
    line-height: 24px;
    color: #fff;
    margin-right: 5px;
}

.btn-view .icon-load {
    width: 38px;
    height: 38px;
    background: transparent;
    float: right;
    border: 0;
    border-left: solid 1px #dedfe5;
    font-size: 16px;
}

.icon-left-big:before {
    content: '\e838';
    -webkit-transform: rotate(180deg);
    -moz-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    -o-transform: rotate(180deg);
    transform: rotate(180deg);
    font-size: 12px;
}

.btn30 .icon-down {
    font-size: 6px;
}

.btn17 .icon-down {
    font-size: 4px;
}

.open .btn30 .icon-down:before, .open.btn30 .icon-down:before, .open .btn17 .icon-down:before, .open.btn17 .icon-down:before {
    content: '\e82f';
}

#register-form .has-feedback .form-control-feedback {
    top: 35px !important;
}

.zoom-1 .block-intent-title-head > div {
    display: block;
}

.zoom-1 .ultimate_goal .intent-title {
    padding-left: 20px;
}

.open .btn30 .icon-down::before {
    content: '\e82e';
}

.wrapper-range-input,
.wrapper-choose-status,
.subwrapper-choose-status {
    padding: 0px;
    margin-bottom: 23px;
}

.wrapper-choose-status {
    /*margin-top: 15px;*/
}

.wrapper-buttons-gantt-popup {
    margin-top: 10px;
}

@media only screen and (max-width: 767px) {
    .navbar-nav .open .dropdown-menu {
        position: absolute !important;
        background-color: #fff !important;
    }
}

/* Smartphones (portrait and landscape) ----------- */
@media only screen and (min-device-width: 320px) and (max-device-width: 480px) {
    .page-size-selector {
        display: none;
    }

    .main-btn-menu .btn-menu {
        top: 15px;
        right: -10px;
        width: 25px;
        height: 25px;
    }

    .favorite_button_top {
        right: 15px;
        top: -15px;
        margin: 0;
        width: 0;
    }

    .favorite_button_top button.btn-star {
        width: 25px;
        height: 25px;
    }

    .tree-table-history {
        width: 100%;
    }

    .main-btn-menu-wrap {
        width: 25px;
    }
}

/* Smartphones (landscape) ----------- */
@media only screen and (min-width: 321px) {
}

/* Smartphones (portrait) ----------- */
@media only screen and (max-width: 320px) {
}

@media only screen and (max-width: 360px) {
    .section-title, h1 {
        font-size: 9px;
    }
}

@media only screen and (min-device-width: 540px) and (max-device-width: 767px) {
    .section-title, h1 {
        font-size: 15px;
    }

    .main-btn-menu .btn-menu {
        top: 15px;
        right: -10px;
        width: 25px;
        height: 25px;
    }

    .favorite_button_top {
        right: 15px;
        top: -15px;
        margin: 0;
        width: 0;
    }

    .favorite_button_top button.btn-star {
        width: 25px;
        height: 25px;
    }

    .main-btn-menu-wrap {
        width: 25px;
    }
}

/* iPads (portrait and landscape) ----------- */
@media only screen and (min-device-width: 768px) and (max-device-width: 1024px) {
    .section-title, h1 {
        font-size: 15px;
    }

    .modile-global-search-block .global-search-block input {
        width: 98.6%;
    }

    .global-search-results {
        width: 97.4% !important;
        margin: 10px;
    }

    .close-global-search {
        position: absolute;
        right: 8px;
        top: 10px;
        color: #ffffff !important;
        cursor: pointer;
        font-size: 17px;
        background-color: #4d8cef;
        width: 25px;
        height: 25px;
        padding: 5px;
        line-height: 0.8;
        text-align: center;
        border-radius: 50%;
    }
}

.col-md-10p {
    max-width: 10%;
    width: 10%;
}

.col-md-15p {
    max-width: 15%;
    width: 15%;
}

.col-md-20p {
    max-width: 20%;
    width: 20%;
}

.col-md-25p {
    max-width: 25%;
    width: 25%;
}

.col-md-30p {
    max-width: 30%;
    width: 30%;
}

.col-md-35p {
    max-width: 35%;
    width: 35%;
}

.col-md-40p {
    max-width: 40%;
    width: 40%;
}

.col-md-45p {
    max-width: 45%;
    width: 45%;
}

.col-md-50p {
    max-width: 50%;
    width: 50%;
}

.col-md-55p {
    max-width: 55%;
    width: 55%;
}

.col-md-60p {
    max-width: 60%;
    width: 60%;
}

.col-md-65p {
    max-width: 65%;
    width: 65%;
}

.col-md-70p {
    max-width: 70%;
    width: 70%;
}

.col-md-75p {
    max-width: 75%;
    width: 75%;
}

.col-md-80p {
    max-width: 80%;
    width: 80%;
}

.col-md-85p {
    max-width: 85%;
    width: 85%;
}

.col-md-90p {
    max-width: 90%;
    width: 90%;
}

.col-md-95p {
    max-width: 95%;
    width: 95%;
}

.col-md-10p,
.col-md-15p,
.col-md-20p,
.col-md-25p,
.col-md-30p,
.col-md-40p,
.col-md-45p,
.col-md-50p,
.col-md-55p,
.col-md-60p,
.col-md-70p,
.col-md-75p,
.col-md-80p,
.col-md-85p,
.col-md-90p,
.col-md-95p {
    padding-right: 10px;
    padding-left: 10px;
}

.row-p {
    margin-right: -10px;
    margin-left: -10px;
}

.action-button {
    position: relative;
    margin-right: 16px;
    min-width: 220px;
    height: 40px;
    padding: 12px 16px 13px 12px;
    border-radius: 4px;
    /* box-shadow: 0 8px 18px 0 rgba(167, 169, 178, 0.39); */
    background-color: #357fed;
}

.action-button-gray {
    position: relative;
    margin-right: 16px;
    min-width: 120px;
    height: 32px;
    padding: 8px 16px 8px 16px;
    border-radius: 4px;
    background-color: #F2F7FF;
}

.action-button-span {
    border: 1px solid red;
}

.action-button-a {
    height: 40px;
    position: absolute;
    top: 0px;
    left: 16px;
    padding-top: 12px;
    font-size: 12px;
    font-weight: 600;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    color: #ffffff !important;
    width: 75%;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
}

.action-button-dropdown{
    width: 270px;
    right: auto !important; 
    left: 0 !important;
}

.action-button-gray .action-button-a {
    height: 40px;
    padding-top: 10px;
    color: #357FED !important;
    width: 70%;
}

.action-button-a i {
    padding-right: 8px;
}

.status-button .action-button-a{
    position: static;
    display: inline-block;
    height: 42px;
    font-size: 12px;
    line-height: 16px;
    padding: 12px 16px;
    width: 100%;
    font-weight: 600;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    color: #357FED !important;
    border: 1px solid #357FED;
    border-radius: 4px;
    overflow: unset;
}

.status-button .action-button-a i {
    margin-left: 12px;
    padding-right: 0px;
}

.btn-carret-action-button {
    position: absolute;
    top: 0px;
    right: 0px;
    width: 40px;
    height: 40px;
    color: #ffffff;
    border: 0;
    background: transparent;
    font-size: 15px;
}

.action-button-gray .btn-carret-action-button {
    color: #357FED;
}

.btn-carret-action-button:before {
    position: absolute;
    top: 10px;
    right: 16px;
    color: #ffffff;
    content: '\f078';
    font-family: 'FontAwesome';
    font-size: 12px;
}

.action-button-gray .btn-carret-action-button:before {
    color: #357FED;
    top: 7px;
}

.btn-carret-action-button:hover {
    color: #ffffff;
}

.action-button-gray .btn-carret-action-button:hover {
    color: #357FED;
}

.commitment-item {
    height: 60px;
    position: relative;
}

.commitment-item .highlight {
    font-weight: 600;
    color: #3c763d;
}

/*
.button-row {
    position: sticky;
    top: 176px;
    z-index: 95;
}
*/

/* table documents */

.table-documents {
    background: #fff;
    border-radius: 4px;
    padding: 0 !important;
    box-shadow: 0 7px 17px 0 rgb(204 207 221 / 30%);
}

.table-documents thead tr th {
    background: #f5f6fa;
    border: 0;
    font-size: 10px;
    font-weight: 600;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    text-transform: uppercase;
    color: #2d374c;
    padding-top: 10px;
    padding-bottom: 10px;
    padding-left: 25px;
}

.table-documents thead tr th a {
    color: #2d374c;
}

.table-documents thead .filters td {
    background: #f5f6fa;
    border-bottom: 1px solid #d9dce2;
    padding-left: 22px;
    padding-top: 0;
    padding-bottom: 10px;
}

.table-documents thead .filters td input {
    height: 44px;
    border-radius: 4px !important;
    border: solid 1px #d9dce2 !important;
}

.table-documents > thead > tr > td {
    border: 0;
}

.table-documents tbody tr td {
    border: 0;
    border-bottom: 1px solid #d9dce2;
    padding-top: 16px;
    padding-bottom: 16px;
    padding-left: 25px;
    font-size: 14px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    color: #1d273e;
}

.draft-status-label {
    padding: 2px 8px 3px;
    border-radius: 2px;
    background-color: #e8e8e8;
    font-size: 10px;
    font-weight: 600;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    color: #1d273e;
}

.draft-status-label + p {
    margin-top: 16px;
    font-size: 14px;
    font-weight: 500;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    color: #df5423;
}

.draft-link-btn {
    width: 30px;
    height: 30px;
    padding-top: 5px;
    border-radius: 4px;
    background-color: #f2f7ff;
    display: inline-block;
    text-align: center;
    margin-right: 15px;
}

.draft-link-btn i {
    font-size: 12px;
}

.select2-container .select2-selection__clear {
    background-color: #fff;
    opacity: .9;
    padding: 0 2px;
}

.select2-container--krajee .select2-dropdown {
    border-color: #f5f6fb !important;
}

.select2-container--krajee-bs3.select2-container--disabled .select2-selection {
    background-color: #F2F7FF !important;
}

.draft-document-date {
    height: 44px;
    border-radius: 4px !important;
    border: solid 1px #d9dce2 !important;
    background-color: #ffffff;
    -webkit-box-shadow: none;
    box-shadow: none !important;
    padding: 11px 24px 6px 12px;

    background-image: url('../images/calendar.svg');
    background-position: right 10px center;
    background-repeat: no-repeat;
}

.draft-document-date::placeholder {
    text-transform: inherit;
    font-size: 14px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    color: #a4b1c8;
}

/*.button-row {*/
/*height: 38px;*/
/*margin-bottom: 8px;*/
/*}*/

.page-visible-btn.center.block-btn-uk {
    width: 393px;
    text-align: center;
}

.page-visible-btn.center.block-btn-en {
    width: 328px;
    text-align: center;
}

.palette-color-red {
    color: #df2323
}

.palette-color-purple {
    color: #1d273e;
}

.ajax-button {
    color: #357fed;
    font-size: 14px;
    text-align: left;
    background: none !important;
    border: none;
    padding: 0 !important;
    cursor: pointer;
    width: 100%;
}

/*.checkbox-wrap {*/
/*    display: none;*/
/*}*/
.checkbox-block {
    display: flex;
    flex-direction: column;
}

.error-settings {
    color: #9c3328;
    font-size: 11px;
    margin-top: -10px;
    margin-bottom: 15px;
}

.btn-sm, .btn-group-sm > .btn {
    padding: 5px 10px !important;
    font-size: 12px !important;;
    line-height: 1.5 !important;;
    border-radius: 3px !important;;
}

.btn-confirm {
    width: 100%;
    font-size: 14px;
    font-weight: 500;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    color: #ffffff;
    background-color: #357fed;
    border-color: #357fed;
    text-transform: none;
    cursor: pointer;
}

.btn-reject {
    width: 100%;
    text-transform: none;
    font-size: 14px;
    font-weight: 500;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    color: #ffffff;
    background-color: #2d374c;
    border-color: #2d374c;
    cursor: pointer;
}

.btn-reject:hover, .btn-confirm:hover {
    color: #ffffff;
}


/* The container */
.container-radio {
    text-transform: none;
    font-size: 14px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    color: #1d273e;
    line-height: 24px;
    display: inline-block;
    position: relative;
    padding-left: 36px;
    padding-right: 12px;
    margin-bottom: 12px;
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

/* Hide the browser's default radio button */
.container-radio input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
}

/* Create a custom radio button */
.checkmark {
    position: absolute;
    top: 0;
    left: 0;
    height: 25px;
    width: 25px;
    background-color: #ffffff;
    border: 1px solid #d9dce2;
    border-radius: 50%;
}

/* On mouse-over, add a grey background color */
.container-radio:hover input ~ .checkmark {
    background-color: #ffffff;
}

/* When the radio button is checked, add a blue background */
.container-radio input:checked ~ .checkmark {
    background-color: #ffffff;
}

/* Create the indicator (the dot/circle - hidden when not checked) */
.checkmark:after {
    content: "";
    position: absolute;
    display: none;
}

/* Show the indicator (dot/circle) when checked */
.container-radio input:checked ~ .checkmark:after {
    display: block;
}

/* Style the indicator (dot/circle) */
.container-radio .checkmark:after {
    top: 8px;
    left: 8px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #357fed;
}

/*
 * File
 */
.file {
    position: relative;
    display: inline-block;
    cursor: pointer;
    width: 100%
}

.file input {
    margin: 0;
    filter: alpha(opacity=0);
    opacity: 0;
}

.file-custom {
    position: absolute;
    top: 0;
    right: -14px;
    left: 0;
    z-index: 5;
    height: 50px;
    padding: 8px 8px;
    width: 100%;

    background-color: #fff;
    border: 1px solid #d9dce2;
    border-radius: 4px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;

    font-size: 14px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 36px;
    color: #a4b1c8;
    text-transform: none;
    overflow-y: hidden;
}

.file-custom:after {
    content: attr(data-file);
}

.file-custom:before {
    position: absolute;
    top: 9px;
    right: 7px;
    bottom: 7px;
    z-index: 6;
    display: block;
    height: 30px;
    padding-left: 8px;
    padding-right: 8px;
    color: #357fed;
    background-color: #f2f7ff;
    border-radius: 4px;
    line-height: 30px;
    content: attr(data-button)
}

/* Focus */
.file input:focus ~ .file-custom {
    box-shadow: 0 0 0 .075rem #fff, 0 0 0 .2rem #0074d9;
}

ul.list li.list-item:before {
    content: "\2022"; /* Add content: \2022 is the CSS Code/unicode for a bullet */
    color: #357fed; /* Change the color */
    font-weight: bold;
    font-size: 1em;
    display: inline-block; /* Needed to add space between the bullet and the text */
    width: 1em; /* Also needed for space (tweak if needed) */
    margin-left: -1em; /* Also needed for space (tweak if needed) */
}

ul.list {
    position: relative;
    list-style: none;
    margin-left: 0;
    padding-left: 1.2em;
}

@media (max-width: 568px) {
    .toggle-small-media {
        display: none;
    }
}

.field-goal-number_auto > .help-block {
    margin: 0px;
}

.field-goal-number_auto > label {
    margin-bottom: 4px;
}

.field-goal-number_auto label input {
    margin: 6px 0 0;
}

.field-goal-number_auto {
    margin-bottom: 0px;
}

.select2-container--krajee-bs3 .select2-selection--single {
    line-height: 2 !important;
}

.select2-container--krajee-bs3 .select2-selection--single {
    height: 42px !important;
}

.select2-container--krajee-bs3 .select2-selection--single .select2-selection__placeholder {
    color: #1d273e !important;
}

.rbm-notifyjs-corner {
    top: 5%;
    left: 10%;
    right: 10%;
}

.donor-select-flag {
    width: 20px;
    height: 15px;
    margin-right: 5px;
}

.donors-projects-logos img {
    width: 20px;
    height: 15px;
    margin-right: 5px;
}

.text-danger {
    color: #DF2323;
}

.bg-light-grey {
    background-color: #f5f6fa !important;
}

.bg-white {
    background-color: #fff !important;
}

.none-shadow {
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
}

.display-none {
    display: none;
}

.vertical-middle,
.vertical-middle td {
    vertical-align: middle !important;
}

.table__scroll {
    display: block;
    overflow-x: auto;
}

.approval__checkbox--wrapper * {
    margin: 0 !important;
}

.block-title-vertical {
    height: 60px;
    display: flex;
    justify-content: left;
    align-items: center;
    font-size: 17px;
    font-weight: bold;
    line-height: 1.35;
    color: #1d273e;
    padding: 0 30px 0 30px;
}

.block-exclusion {
    height: 60px;
    display: flex;
    justify-content: left;
    align-items: center;
    font-size: 17px;
    font-weight: bold;
    line-height: 1.35;
}

.block-title-vertical > h4 {
    color: #1d273e;
}

.block-divider-title-vertical {
    height: 40px;
    display: flex;
    justify-content: left;
    align-items: center;
    background-color: #f5f6fa;
    padding: 0 30px 0 30px;
}

.block-small-text {
    font-size: 10px;
    font-weight: 600;
    line-height: 1.4;
    color: #A4B1C8;
    text-transform: uppercase;
}

.block-title-small-text {
    font-size: 12px;
    text-align: right;
    font-weight: 600;
    line-height: 1.4;
}

.block-user-position {
    font-size: 13px;
    font-weight: 400;
    line-height: 16px;
    color: #667080;
}

.block-divider-title-vertical p {
    margin: unset;
    color: #667080 !important;
}

.flex-wrapper {
    display: flex;
}

.col-25 {
    width: 25%;
}

.col-10p {
    width: 25%;
}

.col-20 {
    width: 20%;
}

.col-15 {
    width: 15%;
}

.col-35 {
    width: 35%;
}

.col-30 {
    width: 30%;
}

.col-40 {
    width: 40%;
}

.col-50 {
    width: 50%;
}

.col-100 {
    width: 100%;
}

.pt-30 {
    padding-top: 30px;
}
.-mt-8{
    margin-top: -8px !important;
}

.-mt-10{
    margin-top: -10px !important;
}

.-mt-24{
    margin-top: -24px !important;
}

.pt-20 {
    padding-top: 20px;
}

.bg-odd {
    background-color: rgba(249, 251, 255, 0.5);
}

.bg-odd .help-block {
    background: unset;
}

.pr-15 {
    padding-right: 15px;
}

.pr-10 {
    padding-right: 10px !important;
}

.pl-15 {
    padding-left: 15px;
}

.mb-0 {
    margin-bottom: 0 !important;
}

.mt-0 {
    margin-top: 0 !important;
}

.info-block-header {
    color: #1D273E;
    margin: 0;
    border-bottom: none;
}

.info-block-sub-header {
    color: #667080;
    background: #F5F6FA;
    padding: 10px 0 10px 10px;
}

.sub-header-text {
    padding: 10px 0;
}

.form-group.last.buttons {
    background: #fff;
    padding: 32px;
    margin-bottom: 0;
    border-radius: 4px;
}

.grid-block.block.info-block p {
    color: #1D273E;
}

.grid-block.block.info-block p.block-small-text {
    color: #A4B1C8 !important;
}

.approval-users-table__header {
    color: #1D273E;
}

.approval-users-table__row .approval-users-table__column:not(:last-child) {
    min-height: 56px;
    border-right: 1px solid #d9dce2;
}

.approval-public-wrapper {
    padding: 0px 24px 15px;
}

.approval-file-wrapper {
    padding: 10px 24px 8px;
}

.approval-public-wrapper.no-x-padding {
    padding-left: 0px;
    padding-right: 0px;
}

.flex-col {
    flex-direction: column;
}

.br-r-light {
    border-right: 2px solid #f5f6fa;
}

.br-r-dark {
    border-right: 2px solid #e6ecf5;
}

.br-b-small {
    border-bottom: 2px solid #e6ecf5;
}

.pt-10 {
    padding-top: 10px !important;
}

.pb-10 {
    padding-bottom: 10px !important;
}

.pb-20 {
    padding-bottom: 20px !important;
}

.pt-5 {
    padding-top: 5px !important;
}

.pt-0 {
    padding-top: 0 !important;
}

.pb-5 {
    padding-bottom: 5px !important;
}

.px-6 {
    padding-left: 6px !important;
    padding-right: 6px !important;
}

.text-warning {
    color: #DF9423;
}

.material-switch > input[type="checkbox"] {
    display: none;
}

.material-switch > label {
    cursor: pointer;
    height: 0px;
    position: relative;
    width: 40px;
    margin-bottom: 5px;
}

.material-switch > label::before {
    background: rgb(0, 0, 0);
    box-shadow: inset 0px 0px 10px rgba(0, 0, 0, 0.5);
    border-radius: 8px;
    content: '';
    height: 16px;
    margin-top: -8px;
    position: absolute;
    opacity: 0.3;
    transition: all 0.4s ease-in-out;
    width: 40px;
}

.material-switch > label::after {
    background: rgb(255, 255, 255);
    border-radius: 16px;
    box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.3);
    content: '';
    height: 24px;
    left: -4px;
    margin-top: -8px;
    position: absolute;
    top: -4px;
    transition: all 0.3s ease-in-out;
    width: 24px;
}

.material-switch > input[type="checkbox"]:checked + label::before {
    background: inherit;
    opacity: 0.5;
}

.material-switch > input[type="checkbox"]:checked + label::after {
    background: inherit;
    left: 20px;
}

.material-switch {
    /* padding: 7px; */
}

.material-switch > span {
    padding-right: 5px;
    color: #333333;
    font-size: 14px;
    font-weight: 500;
}

.number-framed__done {
    padding: 3px;
    margin-right: 5px;
    font-size: smaller;
    border-radius: 3px;
    background: #9EE3BB;
}

.number-framed__delayed {
    padding: 3px;
    margin-right: 5px;
    font-size: smaller;
    border-radius: 3px;
    background: #F1CF9C;
}

.number-framed__canceled {
    padding: 3px;
    margin-right: 5px;
    font-size: smaller;
    border-radius: 3px;
    background: #F19C9C;
}

.text-gray {
    color: #A4B1C8;
}

.text-default {
    color: #1D273E;
}

.sign-folder-modal .modal-dialog {
    max-width: 680px;
    width: max-content;
    padding: 24px 24px 32px 24px;
}

.sign-folder-modal .form-buttons {
    padding: 16px 20px 0px;
}

.sign-folder-modal .form-buttons .btn-confirm {
    width: auto;
    line-height: 16px;
    padding: 12px 32px;
}

.sign-folder-modal .form-buttons .btn-link {
    width: auto;
    line-height: 16px;
    padding: 12px 16px;
}

.resolution-type-select-tabs {
    display: flex;
}

.resolution-type-select-tabs label {
    cursor: pointer;
    width: 33%;
    text-align: center;
    border: 1px solid #d9dce2;
    padding-top: 10px;
    padding-bottom: 10px;
    font-size: 12px;
    text-transform: none;
    font-weight: 400;
    flex: 1 1 auto;
    margin: 0;
}

.resolution-type-select-tabs label:first-child {
    color: #29C168;
    border-radius: 4px 0px 0px 4px;
    border-right: none;
}

.resolution-type-select-tabs label:first-child.checked {
    background: rgba(41, 193, 104, 0.05);
    border: 1px solid #29C168;
}

.resolution-type-select-tabs label:first-child.checked::before {
    font: normal normal normal 12px/1 FontAwesome;
    content: "\f00c";
    margin-right: 3px;
}

.resolution-type-select-tabs label:nth-child(2) {
    color: #DF9423;
    border-right: none;
    border-left: 1px solid #d9dce2;
}

.resolution-type-select-tabs label:nth-child(2).checked {
    background: rgba(223, 148, 35, 0.05);
    border: 1px solid #DF9423;
}

.resolution-type-select-tabs label:nth-child(2).checked::before {
    font: normal normal normal 12px/1 FontAwesome;
    content: "\f0e5";
    margin-right: 3px;
}

.resolution-type-select-tabs label:last-child {
    color: #DF2323;
    border-radius: 0px 4px 4px 0px;
    border: 1px solid #d9dce2 !important;
}

.resolution-type-select-tabs label:last-child.checked {
    background: rgba(223, 35, 35, 0.05);
    border: 1px solid #DF2323 !important;
}

.resolution-type-select-tabs label:last-child.checked::before {
    font: normal normal normal 12px/1 FontAwesome;
    content: "\f05e";
    margin-right: 3px;
}

.resolution-type-select-tabs label > input[type="radio"] {
    display: none;
}

.resolution-type-select-tabs label.disabled {
    color: #A4B1C8 !important;
}

#edsform-resolution {
    display: flex;
}

#edsform-resolution label {
    width: 33%;
    text-align: center;
    border: 1px solid #d9dce2;
    padding-top: 10px;
    padding-bottom: 10px;
    flex: 1 1 auto;
}

#edsform-resolution label:first-child {
    color: #29C168;
    border-radius: 4px 0px 0px 4px;
    border-right: none;
}

#edsform-resolution label:first-child.checked {
    background: rgba(41, 193, 104, 0.05);
    border: 1px solid #29C168;
}

#edsform-resolution label:first-child.checked::before {
    font: normal normal normal 12px/1 FontAwesome;
    content: "\f00c";
}

#edsform-resolution label:nth-child(2) {
    color: #DF9423;
    border-right: none;
    border-left: 1px solid #d9dce2;
}

#edsform-resolution label:nth-child(2).checked {
    background: rgba(223, 148, 35, 0.05);
    border: 1px solid #DF9423;
}

#edsform-resolution label:nth-child(2).checked::before {
    font: normal normal normal 12px/1 FontAwesome;
    content: "\f0e5";
}

#edsform-resolution label:last-child {
    color: #DF2323;
    border-radius: 0px 4px 4px 0px;
    border: 1px solid #d9dce2 !important;
}

#edsform-resolution label:last-child.checked {
    background: rgba(223, 35, 35, 0.05);
    border: 1px solid #DF2323 !important;
}

#edsform-resolution label:last-child.checked::before {
    font: normal normal normal 12px/1 FontAwesome;
    content: "\f05e";
}

#edsform-resolution label > input[type="radio"] {
    display: none;
}

.title-wide {
    margin-right: -25px;
    margin-left: -25px;
    margin-bottom: 24px;
}

.btn-confirm[disabled] {
    border: 1px solid #d9dce2;
    background: #fff;
    color: #A4B1C8;
}

.form-buttons {
    display: flex;
    margin: 0 auto;
    float: none;
}

.form-buttons {
    padding: 0 25%;
    width: 100%;
    align-items: center;
    justify-content: center;
}

.form-buttons button {
    margin: 0 10px;
}

.form-buttons a.btn-reject {
    color: #357FED;
    background: none;
    border: none;
    font-weight: 600;
}

.cbx-disabled {
    background: #d9dce2 !important;
    color: #A4B1C8 !important;
}

.has-success .form-control {
    border-color: #29C168;
    box-shadow: none;
}

.has-success .form-control:focus {
    border-color: #29C168;
    box-shadow: none;
}

.has-error .form-control {
    border-color: #DF2323;
    box-shadow: none;
    background-color: #fff;
}

.has-error .form-control:focus {
    border-color: #DF2323;
    box-shadow: none;
    background-color: #fff;
}

.has-error .help-block {
    color: #DF2323;
}

h4.table_title {
    font-size: 17px;
    color: #1D273E;
}

.delete-responsible {
    text-transform: none !important;
}

.action-title span {
    font-weight: 600;
}

.block-title-vertical h4 {
    font-size: 17px;
}

.info-block .material-switch span {
    color: #1D273E;
}

.info-block .btn-confirm {
    font-weight: 600;
}

.block-user-name {
    color: #1D273E;
}

.level-row-label {
    color: #667080;
}

.bg_shape {
    background-color: #F2F7FF;
}

.permissions-form .select2-container .select2-selection__arrow {
    margin-right: 6px;
    margin-top: 6px;
    height: 30px !important;
    background: #F2F7FF;
    width: 30px !important;
    border: none !important;
    border-radius: 4px;
}

.permissions-form .select2-container .select2-selection__arrow > b {
    border: none !important;

}

.permissions-form .select2-container .select2-selection__arrow::after {
    font: normal normal normal 10px/1 FontAwesome;
    color: #357FED;
    line-height: 10px;
    content: "\f107";
    left: 12px;
    position: relative;
}

.permissions-form .select2-container.select2-container--open .select2-selection__arrow {
    margin-right: 6px;
    margin-top: 6px;
    height: 30px !important;
    background: #357FED !important;
    width: 30px !important;
    border: none !important;
    border-radius: 4px;
}

.permissions-form .select2-container.select2-container--open .select2-selection__arrow::after {
    font: normal normal normal 10px/1 FontAwesome;
    color: #FFFFFF;
    line-height: 10px;
    content: "\f106";
    left: 12px;
    position: relative;
}

.permissions-form .select2-container .select2-selection__clear {
    right: 45px !important;
}

.menu-divider {
    border-top: 1px solid #c1c4d0;
}

input[disabled],
textarea[disabled],
select[disabled] {
    background-color: #F2F7FF !important;
    color: #A4B1C8 !important;
}

.form-control {
    box-shadow: none !important;
}

.bg-odd {
    background-color: rgba(242, 247, 255, 0.5) !important;
}

.block-text-value {
    font-size: 14px !important;
    line-height: 18px;
    margin: 0;
}

.button-row a > span, .permissions-users__menu_btns_inner > a, .mixitup-wrapper-tabs li a {
    text-transform: uppercase;
}

.button-row a, .button-row span {
    font-size: 10px;
}

.flex-auto {
    flex: auto;
}

.border-radius-5 {
    border-radius: 5px;
}

.btn-group {
    padding: 5px;
}

.nowrap {
    white-space: nowrap;
}

.ellipsis {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100px;
    display: block;
}

.d-block {
    display: block;
}

.max-width-100 {
    max-width: 100px;
}

.centered-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
}

.no-mr {
    margin: 0;
}

.w-25 {
    width: 25%;
}

.approval-note-block > span {
    width: 100%;
    display: block;
    height: 50px;
    position: relative;
    background: #FFFEEB;
    border-radius: 4px;
    border: 0.50px #F1CF9C solid;
    padding-left: 16px;
    line-height: 50px;
    color: #1D273E;
    font-weight: 400;
}
.approval-note-block.danger > span {
    background: rgba(223, 35, 35, 0.05);
    border: 0.50px #DF2323 solid;
}

.approval-note-block > span > i {
    left: 16px;
    top: 16px;
    color: #DF9423;
    font-size: 14px;
    font-weight: 900;
    line-height: 50px;
}
.approval-note-block.danger > span > i {
    color: #DF2323;
}

.folder-btn-top {
    color: #357FED;
    font-size: 12px;
    font-family: Source Sans Pro;
    font-weight: 600;
    line-height: 16px;
    word-wrap: break-word;
    height: 40px;
    padding: 12px 16px;
    border-radius: 4px;
    overflow: hidden;
    border: 0.50px #357FED solid;
    background: #fff;
    justify-content: center;
    align-items: center;
    display: inline-flex;
}

.folder-btn-top.active {
    background: #357FED;
    color: #fff;
}

.folder-count-top {
    font-weight: 700;
}

.modal-title h3 {
    text-align: center;
    padding-top: 10px;
    font-family: Source Sans Pro;
    font-size: 17px;
    font-style: normal;
    font-weight: 700;
    line-height: 23px;
    color: #1D273E;
}

.sign-folders {
    /* width: 240px; */
    padding: 12px 16px;
    font-size: 12px;
    line-height: 16px;
    border: none;
    font-weight: 600;
}


.folder_sign_ .modal-title h3{
    margin: 0;
    padding: 24px 0;
}

.folder_sign_ .btn-success{
    min-width: 128px;
}

.approvals-folder-list td {
    position: relative;
}

.table-striped > tbody > tr:nth-of-type(odd) .approval-sign-grid-actions {
    padding-left: 40px;
    background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 1) 25%, rgba(255, 255, 255, 1) 100%);
}

.table-striped > tbody > tr:nth-of-type(even) .approval-sign-grid-actions {
    padding-left: 40px;
    background: linear-gradient(90deg, rgba(242, 247, 255, 0) 0%, rgba(242, 247, 255, 1) 25%, rgba(242, 247, 255, 1) 100%);
}

.table-striped.reverse-stripes > tbody > tr:nth-of-type(even) .approval-sign-grid-actions {
    background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 1) 25%, rgba(255, 255, 255, 1) 100%);
}

.table-striped.reverse-stripes > tbody > tr:nth-of-type(odd) .approval-sign-grid-actions {
    background: linear-gradient(90deg, rgba(242, 247, 255, 0) 0%, rgba(242, 247, 255, 1) 25%, rgba(242, 247, 255, 1) 100%);
}
.approval-sign-grid-actions {
    opacity: 0;
    transition: opacity 0.3s;
    /*background: linear-gradient(90deg, rgba(255, 255, 255, 0.00) 0%, #FFF 100%);*/
    background-color: #FFF;
    height: 100%;
    position: absolute;
    right: 0;
    display: flex;
    flex-shrink: 0;
    top: 0;
}

.xs-button-danger {
    background-color: rgba(223, 35, 35, 0.1);
    color: #DF2323;
}

.xs-button-danger:hover {
    background-color: rgba(223, 35, 35, 0.15);
}

.xs-button-primary {
    background-color: rgba(128, 177, 255, 0.1);
    color: #3D85EE;
}

.xs-button-primary:hover {
    background-color: rgba(128, 177, 255, 0.15);
}

.xs-button-success {
    background-color: rgba(41, 193, 104, 0.1);
    color: #29C168;
}

.xs-button-success:hover {
    background-color: rgba(41, 193, 104, 0.16);
}

.xs-button-warning {
    background-color: rgba(223, 148, 35, 0.1);
    color: #F5A60A;
}

.xs-button-warning:hover {
    background-color: rgba(223, 148, 35, 0.15);
}

tr:hover .approval-sign-grid-actions {
    opacity: 1;
}

.approval-sign-grid-action {
    flex: 1 1 auto;
    padding-top: 6px;
    min-width: 30px;
    height: 30px;
    border-radius: 7px;
    display: flex;
    cursor: pointer;
    justify-content: center;
    align-self: center;
}


.sign-folder-form .blocks {
    display: flex;
    flex-direction: column;
}

.sign-folder-form .icon {
    text-align: center;
    margin: 32px 0 20px 0;
}

.sign-folder-form .announcement {
    text-align: center;
    color: black;
    font-size: 14px;
    font-family: Source Sans Pro;
    font-weight: 400;
    line-height: 18px;
    word-wrap: break-word;
}

.no-margin {
    margin: 0 !important;
}

.word-break {
    word-break: break-word;
}
.status-button {
    /* Kill strange styles */
    box-shadow: none;
    background: none;
    border: none;
    min-width: auto;
    height: auto;
    padding: 0;
}
/* .status-button  .action-button-a {
    box-shadow: none;
    background: none;
    color: #357fed !important;
    width: 100%;
} */
.approval-badge {
    background-color: #FF3B28 !important;
    color: #fff;
    height: 16px;
    min-width: 16px;
    font-weight: 700;
    margin-left: 6px;
    text-decoration: none !important;
    border: none;
    display: inline-block;
    line-height: 16px;
}
#dropdownActionMenu{
    width: 100%;
}

/* New form styles - start */
.form-block .input_color_red{
    color: #DF2323 !important;
    font-weight: 600;
}

.form-block .input_color_green{
    color: #29C168 !important;
    font-weight: 600;
}

.form-block{
    /* display: grid; */
}

.form-block .help-block{
    /* [TODO] Temporary solution / Not good overwrite */
    display: none;
    margin: 0;
    padding-top: 4px;
    background-color: transparent;
}

.form-block .form-group{
    /* [TODO] Temporary solution / Not good overwrite */
    margin: 0;
}

.form-block_color_white{
    background-color: #fff;
}

.form-block__input-group{

}

.form-block__title{
    padding: 16px 24px;
}

.form-block__title-right{
    display: flex;
}

.form-block__title-h{
    font-size: 17px;
    font-style: normal;
    font-weight: 700;
    line-height: 23px;
    color: #1D273E;
}

.form-block__title-label{
    margin-top: 1px;
    font-size: 10px;
    font-style: normal;
    font-weight: 600;
    line-height: 14px;
    color: #A4B1C8;
    border-radius: 2px;
    padding: 4px 8px;
    text-transform: uppercase;
}

.form-block__title-label_outline{
    border: 1px solid #29C168;
    background: transparent;
    color: #29C168;
    padding: 3px 7px 3px 7px;
    margin-left: -2px;
    border-left: 3px solid #29C168;
    border-radius: 0;
}

.form-block__title_align{
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.form-block__secondary-title{
    padding: 12px 24px;
    font-size: 10px;
    font-style: normal;
    font-weight: 600;
    line-height: 14px;
    text-transform: uppercase;
    background: #F5F6FA;
    color: #667080;
}

.form-block__label{
    display: block;
    font-size: 10px;
    font-style: normal;
    font-weight: 600;
    line-height: 14px; /* 140% */
    text-transform: uppercase;
    color: #A4B1C8;
    padding-bottom: 8px;
}

.form-block__label_padding_sm{
    padding-bottom: 4px;
}

.form-block__value{
    display: block;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 18px;
    color: #1D273E;
    word-break: break-word;
}

.form-grid{
    display: grid;
    padding: 24px;
    gap: 24px;
}

.form-grid_col_2{
    grid-template-columns: 1fr 1fr;
}

.form-grid_col_4{
    grid-template-columns: 1fr 1fr 1fr 1fr;
}

.form-grid_gap_sm{
    gap: 16px 24px;
}

.form-grid_bg_gray{
    background-color: rgba(242, 247, 255, 0.5);
}

.form-grid__item{

}

.form-grid__item_col_2{
    grid-column: span 2;
}

.form-grid__item_row_2{
    grid-row: span 2;
}

.form-block .form-control[aria-invalid="true"]+.help-block{
    display: block;
}
/* New form styles - end */