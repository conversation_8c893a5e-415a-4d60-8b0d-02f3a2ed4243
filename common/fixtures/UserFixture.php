<?php
namespace common\fixtures;

use common\models\User;
use common\models\UserEmailActivateTokens;
use Faker\Factory;
use rbm\repositories\UserRepository;
use rbm\useCases\auth\AuthService;
use Yii;
use yii\test\ActiveFixture;


class UserFixture extends ActiveFixture
{
    public $modelClass = User::class;

    public $depends = ['common\fixtures\UserEmailActivateTokensFixture'];

    public function load()
    {
        $fakerEn = Factory::create('en_US');

        $authService = new AuthService(new UserRepository());

        $prefix = Yii::$app->security->generateRandomString(6);

        for ($i = 1; $i <= 10; $i++) {
            $suffix = $i;
            $email = $prefix . '_' . $suffix . '@mail.com';
            $password = $fakerEn->password;
            $firstName = $fakerEn->firstName;
            $lastName = $fakerEn->lastName;
            $user = $authService->createUser($email, $password, $firstName, $lastName, $lastName, 10);

            $user->status = 10;
            $user->is_confirmed_email = 1;
            $user->save(false);

            $role = $user->currentRole;
            $role->position = $fakerEn->jobTitle;
            $role->organization = $fakerEn->company;
            $role->department = $fakerEn->companySuffix;
            $role->save();
        }
    }
}
