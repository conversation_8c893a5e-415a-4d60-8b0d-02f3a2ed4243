<?php

namespace common\fixtures;

use frontend\models\node\BaseNode;
use yii\test\ActiveFixture;


class NodeTasksFixture extends ActiveFixture
{
    public $modelClass = BaseNode::class;

    public $depends = [
        'common\fixtures\NodeActivityFixture',
    ];

    public function load()
    {
        // 1. Получить всех пользователей
        $users = \common\models\User::find()->all();

        // 2. Получить данные задач из фикстуры
        $nodeTaskFixture = new NodeTaskFixture();
        $nodeTaskData = $nodeTaskFixture->getData();

        // Получаем шаблон задачи
        $taskTemplate = $nodeTaskData['nodeTask'] ?? null;

        if (!$taskTemplate || empty($users)) {
            return parent::load();
        }

        // 3. В цикле создать задачи с разными ID и случайными авторами
        $callsCount = 10;
        for ($i = 0; $i < $callsCount; $i++) {
            // Выбираем случайного пользователя
            $randomUser = $users[array_rand($users)];

            // Создаем новую задачу на основе шаблона
            $task = new BaseNode();

            // Копируем данные из шаблона
            foreach ($taskTemplate as $attribute => $value) {
                if ($attribute !== 'id') { // ID будет автоматически назначен
                    $task->$attribute = $value;
                }
            }

            // Изменяем ID автора на случайного пользователя
            $task->id_author = $randomUser->id;

            // Генерируем новый UID для каждой задачи
            $task->uid = \common\components\StrHelper::generateUUID();

            // Изменяем название, чтобы задачи были уникальными
            $task->name_uk = 'Test Task ' . ($i + 1) . ' - ' . rand(1, 1000);
            $task->name_en = 'Test Task ' . ($i + 1) . ' - ' . rand(1, 1000);

            // Сохраняем задачу в БД
            if (!$task->save()) {
                // Логируем ошибки, если есть
                \Yii::error('Failed to save task: ' . json_encode($task->getErrors()));
            }
        }

        return parent::load();
    }


}

