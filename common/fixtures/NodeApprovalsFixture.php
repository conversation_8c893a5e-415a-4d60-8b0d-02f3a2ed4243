<?php

namespace common\fixtures;

use common\components\StrHelper;
use frontend\models\Approval;
use frontend\models\node\BaseNode;
use yii\test\ActiveFixture;


class NodeApprovalsFixture extends ActiveFixture
{
    public $modelClass = Approval::class;

    public $depends = [
        'common\fixtures\NodeTasksFixture',
        'common\fixtures\NodePermissionsFixture',
    ];

//    public $dataFile = '@common/fixtures/data/approval.php';

    public function load()
    {
        $node = BaseNode::findOne(5);

        $approval = new Approval();
        $approval->id_node = $node->id;
        $approval->uid = StrHelper::generateUUID();
        $approval->status = Approval::APPROVAL_STATUS_IN_PROGRESS;
        $approval->id_author = 1;
        $approval->function_name = 'setNodeToDone';
        $approval->field_name = 'status';
        $approval->old_value = '2';
        $approval->new_value = '4';
        $approval->new_value_array = '{"fact_finish":"24.12.2024"}';
        $approval->save(false);

    }
}

