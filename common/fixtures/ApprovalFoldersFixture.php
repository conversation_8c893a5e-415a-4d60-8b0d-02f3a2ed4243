<?php

namespace common\fixtures;

use common\components\StrHelper;
use common\models\permissions\Permissions;
use common\models\User;
use frontend\models\approval\ApprovalFolder;
use frontend\models\ApprovalFolderBaseForm;
use frontend\models\node\BaseNode;
use frontend\models\PermissionConfig;
use frontend\services\PermissionService;
use Yii;
use yii\test\ActiveFixture;


class ApprovalFoldersFixture extends ActiveFixture
{
    public $modelClass = ApprovalFolder::class;

    public $depends = [
        'common\fixtures\NodeApprovalConfirmationDocumentsFixture',
    ];

    public function load()
    {
        $node = BaseNode::findOne(1);

        $folder = new ApprovalFolder();
        $folder->id_approval = 1;
        $folder->uid = StrHelper::generateUUID();
        $folder->uid_level = $node->uid;
        $folder->id_author = 2;
        $folder->status = ApprovalFolderBaseForm::RESOLUTION_TYPE_CONFIRM;
        $folder->comment = 'Integration test approval';
        $folder->created_at = date('Y-m-d H:i:s');
        $folder->save();
    }

}

