<?php

namespace common\fixtures;

use common\models\permissions\Permissions;
use common\models\User;
use frontend\models\node\BaseNode;
use frontend\models\PermissionConfig;
use frontend\services\PermissionService;
use Yii;
use yii\test\ActiveFixture;


class NodePermissionsFixture extends ActiveFixture
{
    public $modelClass = PermissionConfig::class;

    public $depends = [
        'common\fixtures\NodeTaskFixture',
    ];

    public function load()
    {

        $node = BaseNode::findOne(1);
        $user = User::findOne(2);
        $permissionService = Yii::$container->get(PermissionService::class);
        $permissionService->concretizeService($node);
        $permissionService->assignPermissionsToUser($user, $node, [
            Permissions::RESPONSIBLE_TYPE_APPROVE_ACTIVITY,
            Permissions::RESPONSIBLE_TYPE_APPROVE_TASK,
            Permissions::RESPONSIBLE_TYPE_APPROVE_INDICATOR,
            Permissions::RESPONSIBLE_TYPE_MONITORING_ACTIVITY,
            Permissions::RESPONSIBLE_TYPE_MONITORING_TASK,
            Permissions::RESPONSIBLE_TYPE_MONITORING_INDICATOR,
            Permissions::RESPONSIBLE_TYPE_MONITORING_LM,
            Permissions::RESPONSIBLE_TYPE_PLANNING,
            Permissions::RESPONSIBLE_TYPE_GOAL_ADMIN,
            Permissions::RESPONSIBLE_TYPE_APPROVE_LM,
        ], 'test235435545354');

        $node = BaseNode::findOne(5);
        $permissionService->concretizeService($node);
        $permissionService->assignPermissionsToUser($user, $node, [
            Permissions::RESPONSIBLE_TYPE_PLANNING,
            Permissions::RESPONSIBLE_TYPE_MONITORING_TASK,
            Permissions::RESPONSIBLE_TYPE_APPROVE_LM,
            Permissions::RESPONSIBLE_TYPE_APPROVE_TASK,
            Permissions::RESPONSIBLE_TYPE_GOAL_ADMIN,

        ], 'test87687679897898');

    }

}

