<?php
/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.
 * Date: 28.07.2023
 * Time: 14:16
 * @var string $headerTitle
 * @var Approval $approval
 */

use common\components\StrHelper;
use frontend\models\Approval;
use yii\helpers\Html;

?>
<?php if ($approval->isActive()) { ?>
    <?php if ($approval->isRunningOut()) { ?>
        <h4 class="form-block__title-h m-0"><?= StrHelper::firstLetterUp($headerTitle) ?></h4>
        <div class="form-block__title-right m-0">
            <?php
            echo Html::tag("div", mb_strtolower($approval->getTextRemains()), ['class' => 'form-block__title-label bg-running-out',
                'data' => [
                    'toggle' => 'tooltip',
                    'placement' => 'top',
                    'container' => 'body',
                    'html' => 'true',
                    'title' => (DateTime::createFromFormat("Y-m-d H:i:s", $approval->date_finish))->format('d.m.Y H:i'),
                ]
            ]);
            ?>
        </div>
    <?php } else {?>
        <h4 class="form-block__title-h"><?= StrHelper::firstLetterUp($headerTitle) ?></h4>
    <?php } ?>
<?php } else { ?>
    <h4 class="form-block__title-h m-0"><?= StrHelper::firstLetterUp($headerTitle) ?></h4>
    <div class="form-block__title-right m-0">
        <?php
        echo Html::tag("div", mb_strtoupper(Yii::t("app",$approval->getStatusName())), ['class' => 'form-block__title-label bg-' . $approval->getStatusColor()]);
        if ($approval->isConfirmed()) {
            echo Html::tag("div", mb_strtoupper(Yii::t("app", $approval->getRegistrationDate() . " № " . $approval->getRegistrationNumber())), ['class' => 'form-block__title-label form-block__title-label_outline']);
        }
        ?>
    </div>
<?php } ?>