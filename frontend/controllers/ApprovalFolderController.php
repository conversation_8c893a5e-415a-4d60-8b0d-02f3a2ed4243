<?php

namespace frontend\controllers;

use frontend\models\ApprovalFolderBaseForm;
use common\controllers\BaseController;
use frontend\models\Approval;
use frontend\models\approval\ApprovalFolder;
use frontend\models\ApprovalFolderEditForm;
use frontend\services\ApprovalFolderService;
use common\services\LogService;
use DomainException;
use frontend\models\ResolutionForm;
use Yii;
use yii\filters\AccessControl;
use yii\helpers\ArrayHelper;
use yii\web\NotFoundHttpException;
use yii\web\Response;
use yii\widgets\ActiveForm;

class ApprovalFolderController extends BaseController
{
    private ApprovalFolderService $service;

    public function __construct($id, $module, ApprovalFolderService $service, $config = [])
    {
        parent::__construct($id, $module, $config);
        $this->service = $service;
    }

    public function beforeAction($action)
    {
        $this->enableCsrfValidation = false;
        return parent::beforeAction($action);
    }

    /**
     * @return array[]
     */
    public function behaviors(): array
    {
        return [
            'access' => [
                'class' => AccessControl::class,
                'only' => [
                    'update',
                ],
                'rules' => [
                    [
                        'allow' => true,
                        'actions' => [
                            'update',
                        ],
                        'roles' => ['@'],
                    ],
                ],
            ],
        ];
    }

    public function actionCreate(string $uid)
    {
        $formData = Yii::$app->request->post('ResolutionForm');
        $approval = $this->findApproval($uid);
        if (isset($formData['resolutionType'])) {
            $model = $this->service->createFormModelByResolutionType($formData['resolutionType'], $approval);
            if (!is_null($model)) { 
                $model->load(Yii::$app->request->post(), 'ResolutionForm');
                if (Yii::$app->request->isAjax) {
                    $form = new ResolutionForm();
                    $form->load(Yii::$app->request->post());
                    Yii::$app->response->format = Response::FORMAT_JSON;
                    return ActiveForm::validate($form);
                }
                try {
                    $this->service->callSubmitMethod($uid, $model);
                    Yii::$app->session->setFlash("success", Yii::t("approval", "Successfully added to approval folder"));
                } catch (DomainException $e) {
                    Yii::$app->session->setFlash("error", $e->getMessage());
                }
            } else {
                Yii::$app->session->setFlash("error", Yii::t("approval", "An error occurred while added to approval folder"));
            }
        }

        return $this->redirect(['/approval-process']);
    }

    /**
     */
    public function actionDelete()
    {
        if (Yii::$app->request->isPost) {
            $uid = ArrayHelper::getValue(Yii::$app->request->post(), 'uid', '');
            if ($uid && ApprovalFolder::deleteAll(['uid' => $uid])) {
                Yii::$app->session->setFlash("success", Yii::t("approval", "Successfully removed from approval folder"));
                return $this->redirect("/approval/sign");
            }
            Yii::$app->session->setFlash("error", Yii::t('approval', 'folder.approval_not_found'));
            return $this->redirect("/approval/sign");
        }
        throw new NotFoundHttpException(Yii::t('approval', 'folder.approval_not_found'));
    }


    /**
     * @throws NotFoundHttpException
     */
    private function findApproval(string $uid): Approval
    {
        if (($model = Approval::find()->where(['uid' => $uid])->one()) !== null) {
            return $model;
        }

        throw new NotFoundHttpException(Yii::t('approval', 'folder.approval_not_found'));
    }

    public function actionUpdate(string $uid)
    {
        $approvalFolder = ApprovalFolder::findOne(['uid' => $uid]);
        $statusMap = ApprovalFolder::statusMap();
        $formData = Yii::$app->request->post("ResolutionForm");
        $status = ArrayHelper::getValue($formData, 'resolutionType', '');
        $flippedStatuses = array_flip($statusMap);
        if(!empty($status) && $flippedStatuses[$status]) {
            $approvalFolder->setAttributes([
                'status' => $flippedStatuses[$status],
            ]);
        }
        $comment = ArrayHelper::getValue(Yii::$app->request->post('ApprovalFolder'), 'comment', '');
        if(!empty($comment)) {
            $approvalFolder->setAttributes([
                'comment' => $comment,
            ]);
        }
        if ($approvalFolder->status === ApprovalFolderBaseForm::RESOLUTION_TYPE_CONFIRM) {
            $approvalFolder->setAttributes([
                'comment' => '',
            ]);
        }
        $model = new ApprovalFolderEditForm($approvalFolder->approval, ['type' => $approvalFolder->status]);
        $model->load(Yii::$app->request->post(),"ApprovalFolder");

        if (Yii::$app->request->isAjax) {
            Yii::$app->response->format = Response::FORMAT_JSON;
            return ActiveForm::validate($model);
        }
        if ($model->validate()) {
            if (!$approvalFolder->save()) {
                try {
                    LogService::notSaved($approvalFolder);
                    Yii::$app->session->setFlash("error", Yii::t("app", "Error saving data"));
                } catch (DomainException $e) {
                    Yii::$app->session->setFlash("error", $e->getMessage());
                }
            } else {
                try {
                    LogService::saved($approvalFolder);
                    Yii::$app->session->setFlash("success", Yii::t("app", "Saved"));
                } catch (DomainException $e) {
                    Yii::$app->session->setFlash("error", $e->getMessage());
                }
            }
        } else {
            LogService::notValidated($approvalFolder);
        }

        return $this->redirect(['/approval/sign']);
    }

    public function actionValidate(string $uid)
    {
        $approvalFolder = ApprovalFolder::findOne(['uid' => $uid]);
        $statusMap = ApprovalFolder::statusMap();
        $formData = Yii::$app->request->post("ResolutionForm");
        $status = ArrayHelper::getValue($formData, 'resolutionType', '');
        $flippedStatuses = array_flip($statusMap);
        if(!empty($status) && $flippedStatuses[$status]) {
            $approvalFolder->setAttributes([
                'status' => $flippedStatuses[$status],
            ]);
        }
        $comment = ArrayHelper::getValue(Yii::$app->request->post('ApprovalFolder'), 'comment', '');
        if(!empty($comment)) {
            $approvalFolder->setAttributes([
                'comment' => $comment,
            ]);
        }
        if ($approvalFolder->status === ApprovalFolderBaseForm::RESOLUTION_TYPE_CONFIRM) {
            $approvalFolder->setAttributes([
                'comment' => '',
            ]);
        }
        $model = new ApprovalFolderEditForm($approvalFolder->approval, ['type' => $approvalFolder->status]);

        Yii::$app->response->format = Response::FORMAT_JSON;
        $model->load(Yii::$app->request->post(), 'ApprovalFolder');
        return ActiveForm::validate($model);
    }
}