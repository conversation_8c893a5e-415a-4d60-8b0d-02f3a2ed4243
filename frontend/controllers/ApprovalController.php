<?php

namespace frontend\controllers;

use frontend\models\approval_folder_search\ApprovalFolderSearch;
use frontend\models\ApprovalFolderBaseForm;
use frontend\models\ApprovalFolderConfirmForm;
use frontend\models\ApprovalFolderRejectForm;
use frontend\models\ApprovalFolderRemarkForm;
use common\components\StrHelper;
use common\exceptions\DomainException;
use common\exceptions\ForbiddenHttpException;
use frontend\models\Approval;
use frontend\models\approval\ApprovalFolder;
use frontend\models\ApprovalCancellationDocument;
use frontend\models\Goal;
use frontend\models\Models;
use frontend\models\node\BaseNode;
use common\models\User;
use frontend\services\ApprovalFolderService;
use common\services\LogService;
use frontend\models\RejectResolutionForm;
use frontend\models\ResolutionForm;
use Throwable;
use Yii;
use yii\db\ActiveRecord;
use yii\filters\AccessControl;
use yii\helpers\ArrayHelper;
use yii\helpers\Url;
use common\exceptions\HttpException;
use common\exceptions\NotFoundHttpException;
use common\controllers\BaseController;
use common\exceptions\Exception;
use yii\web\Response;
use yii\widgets\ActiveForm;

/**
 * Class ApprovalController
 * @package frontend\controllers
 */
class ApprovalController extends BaseController
{

    /**
     * @return array[]
     */
    public function behaviors()
    {
        return [
            'access' => [
                'class' => AccessControl::class,
                'only' => [
                    'view',
                    'resolution',
                    'sign',
                    'delete-folder',
                ],
                'rules' => [
                    [
                        'allow' => true,
                        'actions' => [
                            'view',
                            'resolution',
                            'sign',
                            'delete-folder',
                        ],
                        'roles' => ['@'],
                    ],
                ],
            ],
        ];
    }

    /**
     * @param $uid
     * @return string
     * @throws NotFoundHttpException
     * @throws Throwable
     * @throws ForbiddenHttpException
     */
    public function actionShow($uid, $level = null)
    {
        $model = $this->findModel($uid);
        if (!$model) {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
        $this->checkAccessByLmStatus(Models::ACT_VIEW, $model);
        $this->allowOnlyIfUsercan(Models::ACT_VIEW, $model);
        if ($level) {
            if ($model->isActive()) {
                $level = BaseNode::getByUID($level);
                if (!$level) {
                    throw new NotFoundHttpException('The requested page does not exist.');
                }
            }

        } elseif ($model->isActive() && !$model->id_reform) {

            if ($item = $model->getLevelUserCanSign() and $model->isUserCanResolutionLevel($item->getModel())) {
                return $this->redirect("/resolution/" . $model->uid . "/" . $item->getModel()->uid);
            }
        }

        $model->expand = true;
        return $this->render("view", [
            'model' => $model,
            'level' => $level
        ]);
    }

    /**
     * @return string
     */
    public function actionSign()
    {
        $user = User::getUser();
        if (Yii::$app->request->isPost) {
            $post = Yii::$app->request->post();
            if (isset($post['action']) && $post['action'] == 'sign') {
                $errors = ApprovalFolder::getInvalidItemsForUser();
                if ($errors) {
                    Yii::$app->session->setFlash("error", Yii::t("approval", "Can't sign") . ": " . implode(",", $errors));
                } else {
                    foreach ($user->approvalFolders as $folderItem) {
                        if (!$folderItem->sign()) {
                            throw new Exception('Error saving data');
                        }
                        Yii::$app->session->setFlash("success", Yii::t("app", "Approved"));
                    }
                }
            }
            return $this->refresh();
        }

        $search = new ApprovalFolderSearch();
        return $this->render('sign', [
            'search' => $search,
            'dataProvider' => $search->search(Yii::$app->request->getQueryParams()),
        ]);
    }

    /**
     * @param $approval
     * @param $level
     * @param ApprovalFolderService $service
     * @return string|Response
     * @throws Exception
     * @throws HttpException
     * @throws NotFoundHttpException
     * @throws Throwable
     * @throws ForbiddenHttpException
     */
    public function actionResolution($approval, $level, $showRedirect = false)
    {
        $actionUrl = Url::to(['/resolution/' . $approval . '/' . $level], true);
        $approvalUid = $approval;
        $approval = $this->findModel($approval);
        if (!$approval) {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
        $linkedModel = $approval->linkedModel;

        if (!$linkedModel) {
            throw new NotFoundHttpException('The requested page does not exist.');
        }

        $chain = $approval->getOrSetChain();

        if (isset($chain->items[$level])) {
            $item = $chain->items[$level];
            $levelModel = $item->getModel();
        }

        if (!$levelModel) {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
        if (!$approval->isUserCanResolutionLevel($levelModel)) {
            return $this->redirect(Url::to(['/approval/' . $approvalUid], true));
        }
        if (Yii::$app->request->isPost) {
            if ($approval->getErrorsForFinish()) {
                Yii::$app->session->setFlash("warning", Yii::t("approval", "This approval cannot be finished."));
                return $this->redirect(Url::to(['/approval/' . $approvalUid], true));
            }
            $service = new ApprovalFolderService();

            $formData = Yii::$app->request->post('ResolutionForm');
            try {
                switch ($formData['resolutionType']) {

                    case ResolutionForm::RESOLUTION_TYPE_CONFIRM:
                        $model = new ApprovalFolderConfirmForm($approval);
                        if ($model->load($formData, '')) {
                            if($model->isLimitReached()){
                                Yii::$app->session->setFlash("warning", Yii::t('approval', 'Approval folder is full'));
                                return $this->refresh();
                            }
                            if ($model->validate()) {
                                $service->confirm($approval->uid, $model, $level);
                                Yii::$app->session->setFlash("success", Yii::t("approval", "Successfully added to approval folder"));
                            } else {
                                Yii::$app->session->setFlash("warning", json_encode($model->errors));
                            }
                        }

                        break;
                    case ResolutionForm::RESOLUTION_TYPE_REMARK:
                        $model = new ApprovalFolderRemarkForm($approval);
                        if ($model->load($formData, '')) {
                            if($model->isLimitReached()){
                                Yii::$app->session->setFlash("warning", Yii::t('approval', 'Approval folder is full'));
                                return $this->refresh();
                            }
                            if ($model->validate()) {
                                $service->remark($approval->uid, $model, $level);
                                Yii::$app->session->setFlash("success", Yii::t("approval", "Successfully added to approval folder"));
                            } else {
                                Yii::$app->session->setFlash("warning", json_encode($model->errors));
                            }
                        }
                        break;
                    case ResolutionForm::RESOLUTION_TYPE_REJECT:
                        $model = new ApprovalFolderRejectForm($approval);
                        if ($model->load($formData, '')) {
                            if($model->isLimitReached()){
                                Yii::$app->session->setFlash("warning", Yii::t('approval', 'Approval folder is full'));
                                return $this->refresh();
                            }
                            if ($model->validate()) {
                                $service->reject($approval->uid, $model, $level);
                                Yii::$app->session->setFlash("success", Yii::t("approval", "Successfully added to approval folder"));
                            } else {
                                Yii::$app->session->setFlash("warning", json_encode($model->errors));
                            }
                        }
                        break;
                }
                return $this->refresh("?showRedirect=true");
            } catch (DomainException $e) {
                Yii::$app->session->setFlash("error", $e->getMessage());
            }
            return $this->refresh();
        }


        $this->allowOnlyIfUsercan(Models::ACT_VIEW, $levelModel);
        $allow = $item->isUserResponsible();
        if (!$allow) {
            throw new HttpException(403);
        }
        if ($approval and $levelModel and $linkedModel) {

            $user = User::getUser();

            return $this->render('resolution/view', [
                'model' => $linkedModel,
                'showRedirect' => $showRedirect,
                'approval' => $approval,
                'level' => $levelModel,
                'user' => $user,
                'actionUrl' => $actionUrl
            ]);
        }

        throw new NotFoundHttpException('The requested page does not exist.');
    }

    /**
     * @param $approval
     * @param $level
     * @param ApprovalFolderService $service
     * @return string|Response
     * @throws Exception
     * @throws HttpException
     * @throws NotFoundHttpException
     * @throws Throwable
     * @throws ForbiddenHttpException
     */
    public function actionValidate($uid)
    {
        $approval = $this->findModel($uid);
        if (!$approval) {
            throw new NotFoundHttpException('The requested page does not exist.');
        }

        if (Yii::$app->request->isPost) {
            $formData = Yii::$app->request->post('ResolutionForm');
            switch ($formData['resolutionType']) {

                case ResolutionForm::RESOLUTION_TYPE_CONFIRM:
                    $model = new ResolutionForm();
                    $model->resolutionType = ApprovalFolderBaseForm::RESOLUTION_TYPE_CONFIRM;
                    break;
                case ResolutionForm::RESOLUTION_TYPE_REMARK:
                    $model = new ResolutionForm();
                    $model->resolutionType = ApprovalFolderBaseForm::RESOLUTION_TYPE_REMARK;
                    break;
                case ResolutionForm::RESOLUTION_TYPE_REJECT:
                    $model = new ResolutionForm();
                    $model->resolutionType = ApprovalFolderBaseForm::RESOLUTION_TYPE_REJECT;

                    break;
            }
            $model->load(Yii::$app->request->post());
            $result = ActiveForm::validate($model);
            return $this->asJson($result);
        }


        throw new NotFoundHttpException('The requested page does not exist.');
    }

    /**
     * @param $uid
     * @return array|Models|null|ActiveRecord
     * @throws NotFoundHttpException
     */
    private function findModel($uid): Approval
    {
        $model = Approval::getByUID($uid);
        if ($model !== null && !$model->isDeleted()) {
            return $model;
        }

        LogService::notFound(Goal::class, ['uid' => $uid]);
        throw new NotFoundHttpException(Yii::t('app', 'The requested page does not exist.'));
    }

    public function actionReject(string $uid)
    {
        $approval = $this->findModel($uid);

        if (!$approval || !$approval->isCanQuickReject() || !$approval->isActive()) {
            throw new NotFoundHttpException('The requested page does not exist.');
        }

        if (Yii::$app->request->isPost && $post = Yii::$app->request->post()) {
            $form = new RejectResolutionForm();

            if ($form->load($post)) {
                if ($form->validate()) {
                    try {
                        $user = User::getUser();
                        $model = new ApprovalCancellationDocument([
                            'uid' => StrHelper::generateUUID(),
                            'id_approval' => $approval->id,
                            'id_user' => $user->id,
                            'description' => $form->comment ?? '',
                        ]);
                        LogService::created($model);
                        $model->saveOrFail();
                        $approval->cancelled();
                        Yii::$app->session->setFlash("warning", Yii::t("app", "Cancelled"));
                    } catch (Throwable $e) {
                        LogService::notCreated($model);
                        Yii::error($e->getMessage());
                        Yii::$app->session->setFlash("error", Yii::t("app", "Error saving data: " . $e->getMessage()));
                    }
                } else {
                    LogService::notValidatedForm($form);
                    Yii::$app->session->setFlash("error", Yii::t("app", "Validate form error"));
                }
            }

            return $this->redirect($approval->getUrl());
        }

        throw new NotFoundHttpException('The requested page does not exist.');

    }

    /**
     * @param string $uid
     * @return Response
     * @throws NotFoundHttpException
     */
    public function actionValidateReject(string $uid)
    {
        $approval = $this->findModel($uid);

        if (!$approval || !$approval->isCanQuickReject() || !$approval->isActive()) {
            throw new NotFoundHttpException('The requested page does not exist.');
        }

        if (Yii::$app->request->isPost && $post = Yii::$app->request->post()) {
            $form = new RejectResolutionForm();

            if ($form->load($post)) {
                return $this->asJson(ActiveForm::validate($form));
            }
        }

        throw new NotFoundHttpException('The requested page does not exist.');

    }
}
