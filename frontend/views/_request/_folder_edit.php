<?php


use common\components\StrHelper;
use common\components\forms\ActiveForm;
use frontend\models\Approval;
use frontend\models\approval\ApprovalChain;
use frontend\models\approval\ApprovalFolder;
use frontend\models\ApprovalFolderBaseForm;
use frontend\widgets\modal\Modal;
use frontend\models\ResolutionForm;
use yii\helpers\Html;
$statusMap = [
    ApprovalFolderBaseForm::RESOLUTION_TYPE_CONFIRM => ResolutionForm::RESOLUTION_TYPE_CONFIRM,
    ApprovalFolderBaseForm::RESOLUTION_TYPE_REMARK => ResolutionForm::RESOLUTION_TYPE_REMARK,
    ApprovalFolderBaseForm::RESOLUTION_TYPE_REJECT => ResolutionForm::RESOLUTION_TYPE_REJECT,
];
$approvalFolder = ApprovalFolder::getByUID($id);
if(!$approvalFolder){
    echo $this->render("bad_request");
    return;
}
$formModel = new ResolutionForm();
$formModel->resolutionType = $statusMap[$approvalFolder->status];
$approval = $approvalFolder->approval;
$chain = $approval->getOrSetChain();
if (empty($approvalFolder->uid_level) || !isset($chain->items[$approvalFolder->uid_level])) {
    echo $this->render("bad_request");
    return;
}

$item = $chain->items[$approvalFolder->uid_level];
$level = $item->getModel();
$formModel->lastLevel = $approval->isLastLevel($level);
$model = $approval->linkedModel;
$needConfirmWarning = (int)$approval->isConfirmWarningNeeded($level);
$confirmMessage = Yii::t('app', 'This is the final decision that will complete the approval process');
$needRejectWarning = (int)$approval->isRejectWarningNeeded($level);
$rejectMessage = Yii::t('app', 'This is the final decision that will cancel the approval process');

if ($needConfirmWarning) {
    $confirmButtonTitle = Yii::t('eds', 'Finally confirm');
} else {
    $confirmButtonTitle = Yii::t('eds', 'Confirm');
}
$remarkButtonTitle = Yii::t("approval", "approval.action." . Approval::APPROVAL_STATUS_REMARKED);
if ($needRejectWarning) {
    $rejectButtonTitle = Yii::t('eds', 'Finally reject');
} else {
    $rejectButtonTitle = Yii::t('eds', 'Reject');
}
$defaultButtonTitle = Yii::t('app', 'Save');

ob_start();

?>
    <div class="row no-padding">
        <?php
        $form = ActiveForm::begin([
            'method' => 'post',
            'options' => ['class' => 'form-multiple dont-disable-submit-btn'],
            'id' => 'resolution-form',
            'action' => ['/approval-folder/update', 'uid' => $approvalFolder->getUid()],
            'enableAjaxValidation' => true,
            'validateOnBlur' => false,
            'validateOnChange' => false,
            'validateOnSubmit' => true,
            'enableClientValidation' => false,
            'validationUrl' => ['/approval-folder/validate', 'uid' => $approvalFolder->getUid()],
        ]); ?>


        <div class="col-md-12">
            <p class="block-small-text mb-4">
                <?php echo Yii::t('approval', 'grid.type.full'); ?>
            </p>
            <p class="mb-4">
                <?php echo $approval->getActionType(); ?>
            </p>
        </div>
        <div class="col-md-12 mt-10 mb-10">
            <p class="block-small-text mb-4">
                <?php echo Yii::t('approval', 'grid.name'); ?>
            </p>
            <p class="mb-4">
                <?php echo $approval->link; ?>
            </p>
        </div>

        <div class="col-md-12">
            <div class="alert alert-danger alert-dismissible" style="display:none; word-wrap: break-word;"
                 id="alert-danger">
            </div>
        </div>

        <div class="col-md-12" id="resolutionBlock">
            <?= $form->field($formModel, 'resolutionType')->radioList($formModel->resolutionList($needConfirmWarning, $needRejectWarning), [
                'placeholder' => Yii::t('eds', 'Select resolution type'), 'class' => 'resolution-type-select-tabs'
            ])->label(false) ?>
        </div>

        <div class="col-md-12" id="commentBlock"
             style="display:<?php echo $formModel->resolutionType !== ResolutionForm::RESOLUTION_TYPE_CONFIRM ? 'block' : 'none'; ?>">
            <?= $form->field($approvalFolder, 'comment')->textarea(['rows' => 6, 'id' => 'approvalfoldereditform-comment'])->label() ?>
        </div>
        <?php
        $display = 'block';
        if ($formModel->resolutionType == ResolutionForm::RESOLUTION_TYPE_REMARK) {
            $display = "none";
        } else {
            if ($formModel->resolutionType == ResolutionForm::RESOLUTION_TYPE_CONFIRM && !$needConfirmWarning) {
                $display = "none";
            }
            if ($formModel->resolutionType == ResolutionForm::RESOLUTION_TYPE_REJECT && !$needRejectWarning) {
                $display = "none";
            }
        }

        ?>
        <div class="col-md-12 mb-10 approval-note-block" id="agree-note-block" style="display: <?php echo $display; ?>">
            <span>
                <i class="fa-solid fa-triangle-exclamation mr-10"></i>
                <span class="info-span"><?php echo Yii::t('app', 'This is the final decision that will complete the approval process') ?></span>
            </span>
        </div>

        <?= $this->render("/common/approval/changed_date_warning", ['approval' => $approval,'class'=>"col-md-12 danger approval-note-block mb-10"]) ?>

        <div class="col-md-12 buttons mt-10">
            <?= Html::submitButton($defaultButtonTitle, [
                'class' => 'btn btn-primary',
            ]) ?>
            <?= Html::submitButton(Yii::t('app', 'Close'), ['class' => 'btn-link-cancel btn btn-link', "data-dismiss" => "modal"]) ?>
        </div>

        <?php
        ActiveForm::end();
        ?>
    </div>
<?php

$this->registerJs(<<<JS
  var select = $('#resolutionform-resolutiontype :radio'),
        signButton = $('#sign-button'),
        agreeNoteBlock = $('#agree-note-block'),
        agreeNoteBlockMessage = agreeNoteBlock.find('span.info-span'),
        confirmOpt = 'confirm',
        rejectOpt = 'reject';
        remarkOpt = 'remark';

  signButton.prop('disabled', true);

  select.click(function() {
      // $('.approval__checkbox--wrapper label').text('');
      $('#resolutionform-resolutiontype label').removeClass('checked');
    let selectedValue = $(this).val();
    $(this).parent().addClass('checked');
    if ((selectedValue === rejectOpt) || (selectedValue === remarkOpt)) {
        $('#commentBlock').show();
    } else {
        $('#commentBlock').hide();
    }
    if(selectedValue === confirmOpt){
        signButton.text('$confirmButtonTitle');
    } else if(selectedValue == rejectOpt){
        signButton.text("$rejectButtonTitle");
    } else {
        signButton.text("$defaultButtonTitle");
    }
    if (($needConfirmWarning && (selectedValue === confirmOpt))
        || ($needRejectWarning && (selectedValue === rejectOpt))
    ) {
      let message = (selectedValue === confirmOpt) ? '$confirmMessage' : '$rejectMessage';
      signButton.prop('disabled', false);
      agreeNoteBlockMessage.text(message);
      agreeNoteBlock.show();

    } else {
      agreeNoteBlock.hide();
      signButton.prop('disabled', false);
    }
  })
JS
);
?>
<?php
$this->registerJs(<<<JS
  $('#resolutionform-resolutiontype input:checked').parent().addClass('checked');
JS
);
echo Modal::widget([
    'id' => 'modal_form',
    'closeButton' => false,
    'title'=>Yii::t("app","Change decision"),
    'content' => ob_get_clean(),
    'size'=>'big'
]);
//echo StrHelper::modalBox('', ob_get_clean(), ['class' => 'sign-folder-modal', 'non-overflow' => true]);