<?php

namespace frontend\models;

use common\behaviors\CacheBehavior;
use common\components\cache\CacheComponent;
use common\components\StrHelper;
use common\helpers\LanguageHelper;
use common\models\User;
use common\services\EnvService;
use common\services\LogService;
use frontend\models\node\BaseNode;
use frontend\services\catalogue\ICatalogueItemDto;
use frontend\services\CatalogueService;
use Yii;
use yii\db\ActiveQuery;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "commitment_node".
 *
 * @property int $id
 * @property int $id_node
 * @property string $name_uk
 * @property string $name_en
 * @property string $short_uk
 * @property string $short_en
 * @property string $created_at
 * @property string $updated_at
 * @property string $commitment_identifier
 * @property int $id_author
 * @property BaseNode $node
 */
class CommitmentNode extends Models
{
    public string $groupIdentifier = '';

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'commitment_node';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['id_node', 'id_author'], 'integer'],
            [['commitment_identifier', 'name_uk', 'name_en', 'short_uk', 'short_en'], 'string'],
            [['id_node', 'id_author'], 'required'],
            [['commitment_identifier'], 'in', 'range' => (Yii::$container->get(CatalogueService::class))->forCommitments()->getAvailableIdentifiers()],
        ];
    }

    public function beforeValidate()
    {
        if (!$this->id_author) {
            if ($user = User::getUser()) {
                $this->id_author = $user->id;
            } else {
                LogService::notFound(User::class, []);
            }
        }
        return parent::beforeValidate();
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => Yii::t('app', 'ID'),
            'id_node' => Yii::t('app', 'Node'),
            'name_uk' => Yii::t('app', 'Name Uk'),
            'name_en' => Yii::t('app', 'Name En'),
            'short_uk' => Yii::t('app', 'Short Uk'),
            'short_en' => Yii::t('app', 'Short En'),
            'id_author' => Yii::t('app', 'Author'),
            'commitment_identifier' => Yii::t('app', 'Commitment')
        ];
    }

    public function behaviors(): array
    {
        $behaviors = [
            'CacheBehavior' => [
                'class' => CacheBehavior::className(),
            ],
            'timestamp' => [
                'class' => 'yii\behaviors\TimestampBehavior',
                'attributes' => [
                    ActiveRecord::EVENT_BEFORE_INSERT => ['created_at', 'updated_at'],
                    ActiveRecord::EVENT_BEFORE_UPDATE => ['updated_at'],
                ],
                'value' => date("Y-m-d H:i:s"),
            ],
        ];

        return array_merge(parent::behaviors(), $behaviors);
    }

    public function getAllCacheKeys()
    {
        return [
            CacheComponent::CACHE_NODE_LINK_COMMITMENTS . $this->id_node,
        ];
    }


    /**
     * @return ActiveQuery
     */
    public function getCommitment(): ?ICatalogueItemDto
    {
        $service = Yii::$container->get(CatalogueService::class);
        $item = $service->forCommitments()->getOne($this->commitment_identifier);
        // if no name from catalog get name from db
        if ($item && empty($item->getTitle())) {
            $item->setTitle($this->name_uk);
        }
        return $item;
    }
    /**
     * @var array
     */
    public $_structure = [
        [
            'name' => 'name_uk',
            'type' => Models::IMPORT_TYPE_TEXT,
        ],
        [
            'name' => 'name_en',
            'type' => Models::IMPORT_TYPE_TEXT,
        ],
        [
            'name' => 'short_uk',
            'type' => Models::IMPORT_TYPE_TEXT,
        ],
        [
            'name' => 'short_en',
            'type' => Models::IMPORT_TYPE_TEXT,
        ],
        [
            'name' => 'commitment_identifier',
            'type' => Models::IMPORT_TYPE_TEXT,
        ],
        [
            'name' => 'commitment_parent',
            'type' => Models::IMPORT_TYPE_TEXT,
        ],
    ];

    /**
     * @param $addTitle
     * @param $full
     * @param $progress
     * @return array
     */
    public function generateExport($addTitle = true, $full = true, $progress = true): array
    {
        $array = [];
        if ($this->_structure) {
            $this->prepareStructure();
            foreach ($this->_structure as $structureItem) {
                if (!is_null($this->commitment)){
                    $item = $this->commitment;
                    switch ($structureItem['name']) {
                        case 'name_uk':
                            if (strlen((string)$item->getTitle()) === 0) {
                                $array[$structureItem['name']] = $this->name_uk ?? '';
                            } else {
                                $array[$structureItem['name']] = $item->getTitle() ?? '';
                            }
                            break;
                        case 'name_en':
                            if (strlen((string)$item->getNameEn()) === 0) {
                                $array[$structureItem['name']] = $this->name_en ?? '';
                            } else {
                                $array[$structureItem['name']] = $item->getNameEn() ?? '';
                            }
                            break;
                        case 'short_uk':
                            if (strlen((string)$item->getShortUk()) === 0) {
                                $array[$structureItem['name']] = $this->short_uk ?? '';
                            } else {
                                $array[$structureItem['name']] = $item->getShortUk() ?? '';
                            }
                            break;
                        case 'short_en':
                            if (strlen((string)$item->getShortEn()) === 0) {
                                $array[$structureItem['name']] = $this->short_en ?? '';
                            } else {
                                $array[$structureItem['name']] = $item->getShortEn() ?? '';
                            }
                            break;
                        case 'commitment_identifier':
                            $array[$structureItem['name']] = $item->getIdentifier() ?? '';
                            break;
                        case 'commitment_parent':
                            $array[$structureItem['name']] = $item->getParent() ?? '';
                            break;

                    }

                }

            }

        }

        return $array;

    }

    public function getNode(): ActiveQuery
    {
        return $this->hasOne(BaseNode::class, ['id' => 'id_node']);
    }

    public function setShortNameLang($name)
    {
        if (LanguageHelper::isLangUk()) {
            $this->short_uk = $name;
        } else {
            $this->short_en = $name;
        }
    }

    public function beforeSave($insert)
    {
        //Add id_author for all possible cases where it is not set
        if (empty($this->id_author)) {
            $this->id_author = User::getUser() ? User::getUser()->id : null;
        }

        return parent::beforeSave($insert);
    }
}