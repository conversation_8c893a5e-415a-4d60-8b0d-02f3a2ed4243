<?php

namespace frontend\models\approval;

use frontend\models\ApprovalFolderBaseForm;
use common\components\StrHelper;
use common\exceptions\NotFoundException;
use frontend\models\Approval;
use frontend\models\ApprovalHistory;
use frontend\models\Indicator;
use frontend\models\Models;
use frontend\models\node\BaseNode;
use frontend\models\Reform;
use common\models\User;
use frontend\models\ResolutionForm;
use Throwable;
use Yii;
use yii\base\InvalidConfigException;
use yii\base\NotSupportedException;
use yii\behaviors\AttributeBehavior;
use yii\behaviors\TimestampBehavior;
use yii\db\ActiveQuery;
use yii\db\ActiveRecord;
use yii\db\Exception;
use yii\db\StaleObjectException;
use yii\db\Transaction;
use yii\helpers\HtmlPurifier;

/**
 * This is the model class for table "approval_folder".
 *
 * @property int $id
 * @property string $uid
 * @property int $id_approval
 * @property string $created_at
 * @property string $updated_at
 * @property int $id_author
 * @property string $uid_level
 * @property string $comment
 * @property string $status
 *
 * @property Approval $approval
 * @property User $author
 */
class ApprovalFolder extends Models
{
    /**
     * {@inheritdoc}
     */
    public static function tableName(): string
    {
        return 'approval_folder';
    }

    /**
     * {@inheritdoc}
     */
    public function rules(): array
    {
        return [
            [['id_approval', 'id_author'], 'integer'],
            [['created_at', 'updated_at'], 'safe'],
            [['comment', 'status'], 'string', 'max' => 500],
            [['uid', 'uid_level'], 'string', 'max' => 38],
            [['id_approval'], 'exist', 'skipOnError' => true, 'targetClass' => Approval::className(), 'targetAttribute' => ['id_approval' => 'id']],
            [['id_author'], 'exist', 'skipOnError' => true, 'targetClass' => User::className(), 'targetAttribute' => ['id_author' => 'id']],
            [['comment'], 'required', 'when' => static fn(self $model) => ($model->status === ApprovalFolderBaseForm::RESOLUTION_TYPE_REMARK || $model->status === ApprovalFolderBaseForm::RESOLUTION_TYPE_REJECT)]

        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => Yii::t('app', 'ID'),
            'uid' => Yii::t('app', 'Uid'),
            'id_approval' => Yii::t('app', 'Id Approval'),
            'created_at' => Yii::t('app', 'Created At'),
            'updated_at' => Yii::t('app', 'Updated At'),
            'id_author' => Yii::t('app', 'Id Author'),
            'uid_level' => Yii::t('app', 'Uid Level'),
            'comment' => Yii::t('app', 'Comment'),
            'status' => Yii::t('app', 'Status'),
        ];
    }

    /**
     * @return array[]
     */
    public function behaviors(): array
    {
        $behaviors = [
            'timestamp' => [
                'class' => TimestampBehavior::class,
                'attributes' => [
                    ActiveRecord::EVENT_BEFORE_INSERT => ['created_at', 'updated_at'],
                    ActiveRecord::EVENT_BEFORE_UPDATE => ['updated_at'],
                ],
                'value' => date("Y-m-d H:i:s"),
            ],
            [
                'class' => AttributeBehavior::class,
                'attributes' => [
                    ActiveRecord::EVENT_BEFORE_VALIDATE => 'id_author',
                ],
                'value' => function ($event) {
                    if (!$this->id_author) {
                        $user = User::getUser();
                        $this->id_author = $user ? $user->id : null;
                    }
                    return $this->id_author;
                },
            ],
            [
                'class' => AttributeBehavior::class,
                'attributes' => [
                    ActiveRecord::EVENT_BEFORE_VALIDATE => 'uid',
                ],
                'value' => function ($event) {
                    if (!$this->uid) {
                        $this->uid = StrHelper::generateUUID();
                    }
                    return $this->uid;
                },
            ],
        ];
        return array_merge(parent::behaviors(), $behaviors);
    }


    /**
     * @return ActiveQuery
     */
    public function getApproval()
    {
        return $this->hasOne(Approval::className(), ['id' => 'id_approval']);
    }

    /**
     * @return ActiveQuery
     */
    public function getAuthor()
    {
        return $this->hasOne(User::className(), ['id' => 'id_author']);
    }

    public static function create(int $idApproval, string $uidLevel, string $status, ?string $comment = null): ?self
    {
        $user = User::getUser();
        if (!self::findOne([
            'id_approval' => $idApproval,
            'uid_level' => $uidLevel,
            'status' => $status,
            'comment' => $comment,
            'id_author' => $user ? $user->id : null
        ])) {
            $model = new self();
            $model->id_approval = $idApproval;
            $model->uid_level = $uidLevel;
            $model->status = $status;
            $model->comment = $comment;

            return $model;
        }
        return null;

    }


    /**
     * @return bool
     * @throws InvalidConfigException
     * @throws NotSupportedException
     * @throws Exception
     */
    public function sign(): bool
    {
        $transaction = Yii::$app->db->beginTransaction(Transaction::READ_COMMITTED);
        try {
            $user = User::getUser();
            $level = $this->approval->getLevelUserCanSign();
            $levelModel = $level->getModel();
            $history = new ApprovalHistory();
            $history->id_author = $user->id;
            $history->uid = StrHelper::generateUUID();
            $history->result = StrHelper::smart_trim(HtmlPurifier::process($this->comment), 500);
            $history->id_approval = $this->id_approval;
            $history->uid_level = $this->uid_level;
            try {
                $levelModel = BaseNode::getByUID($this->uid_level);
                $history->id_node = $levelModel->id;
            } catch (NotFoundException $e) {
                $levelModel = Indicator::getByUID($this->uid_level);
                $history->id_indicator = $levelModel->id;
            }
            $history->status = $this->status;
            if ($history->save()) {
                $this->delete();
                $transaction->commit();
            } else {
                $transaction->rollBack();
                return false;
            }
            return true;
        } catch (Throwable $e) {
            Yii::error($e->getMessage());
            $transaction->rollBack();
            return false;
        }
        $transaction->rollBack();
    }

    public function getErrorsForUser()
    {
        /** @var Approval $approval */
        $approval = $this->approval;
        if ($level = $approval->getLevelUserCanSign()) {
            if (!$approval->isUserCanResolutionLevelByUID($this->uid_level)) {
                return Yii::t("approval", "This level cannot be signed");
            } elseif ($approval->getErrorsForFinish()) {
                return Yii::t("approval", "Approval cannot be finished at now");
            }
        } else {
            return Yii::t("approval", "No level to sign");
        }
        return false;
    }

    public static function getInvalidItemsForUser()
    {
        $user = User::getUser();
        $folder = $user->approvalFolders;
        $errors = [];
        /** @var ApprovalFolder $folderItem */
        foreach ($folder as $folderItem) {
            if ($error = $folderItem->getErrorsForUser()) {
                $errors[] = $error;
            }
        }
        return $errors;
    }

    public static function statusMap()
    {
        return [
            ApprovalFolderBaseForm::RESOLUTION_TYPE_CONFIRM => ResolutionForm::RESOLUTION_TYPE_CONFIRM,
            ApprovalFolderBaseForm::RESOLUTION_TYPE_REMARK => ResolutionForm::RESOLUTION_TYPE_REMARK,
            ApprovalFolderBaseForm::RESOLUTION_TYPE_REJECT => ResolutionForm::RESOLUTION_TYPE_REJECT,
        ];

    }

    public function isRejectWarningNeeded(): bool
    {
        $level = $this->levelModel;
        return $this->approval->isRejectWarningNeeded($level);
    }

    public function isConfirmWarningNeeded(): bool
    {
        $level = $this->levelModel;
        return $this->approval->isConfirmWarningNeeded($level);
    }

    public function getLevelModel()
    {
        $model = null;

        if ($uid = $this->uid_level) {
            try {
                $model = BaseNode::getByUID($uid);
            } catch (NotFoundException $e) {
                try {
                    $model = Indicator::getByUID($uid);
                } catch (NotFoundException $e) {
                    $model = Reform::getByUID($uid);
                }
            }
        }

        return $model;
    }
}